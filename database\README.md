# قاعدة البيانات المؤقتة لموقع Rix

## 📁 هيكل الملفات

```
database/
├── users.json          # بيانات المستخدمين
├── tasks.json          # المهام والمهام اليومية
├── codes.json          # الأكواد وأكواد الأدمن
├── withdrawals.json    # طلبات السحب
├── stats.json          # الإحصائيات العامة
├── config.json         # إعدادات النظام
└── README.md          # هذا الملف
```

## 🔧 كيفية الاستخدام

### 1. تحميل البيانات
```javascript
// تحميل جميع البيانات
await db.loadInitialData();

// تحميل نوع معين من البيانات
const users = await db.getData('users');
const tasks = await db.getData('tasks');
```

### 2. البحث والاستعلام
```javascript
// البحث عن مستخدم
const user = await db.findUser('username', 'password');
const userById = await db.findUserById(123);

// الحصول على مهام المستخدم
const userTasks = await db.getUserTasks('username', userLevel);

// التحقق من صحة كود
const code = await db.validateCode('RIX100', 'username');
```

### 3. حفظ البيانات
```javascript
// حفظ البيانات (محاكاة)
await db.saveData('users', userData);
await db.saveData('tasks', tasksData);
```

## 📊 بنية البيانات

### المستخدمين (users.json)
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "123456",
  "balance": 250,
  "totalEarned": 500,
  "level": 3,
  "isAdmin": false,
  "isVIP": false,
  "joinDate": "2024-12-01T10:30:00.000Z",
  "lastLogin": "2024-12-29T11:45:00.000Z",
  "completedTasks": [1, 2, 3],
  "usedCodes": ["RIX100", "WELCOME50"],
  "inviteStats": {
    "total": 3,
    "active": 2,
    "earnings": 75
  },
  "withdrawHistory": [],
  "achievements": ["first_task"],
  "status": "active"
}
```

### المهام (tasks.json)
```json
{
  "id": 1,
  "title": "تابع صفحتنا على فيسبوك",
  "description": "تابع صفحة Rix الرسمية",
  "reward": 25,
  "type": "social",
  "difficulty": "easy",
  "link": "https://facebook.com/rix.official",
  "duration": 60,
  "requireLogin": true,
  "trackTime": true,
  "isActive": true,
  "target": "all",
  "createdBy": "admin",
  "completedBy": [2, 3, 4],
  "totalCompletions": 3
}
```

### الأكواد (codes.json)
```json
{
  "id": 1,
  "code": "RIX100",
  "points": 100,
  "description": "كود ترحيبي",
  "type": "welcome",
  "rarity": "common",
  "maxUses": 1000,
  "currentUses": 156,
  "isActive": true,
  "createdBy": "admin",
  "expiryDate": null,
  "usedBy": [2, 3, 5]
}
```

## 🔒 الأمان

### حسابات افتراضية:
- **الأدمن**: `admin` / `admin123`
- **مستخدم تجريبي**: `testuser` / `123456`

### ميزات الأمان:
- تشفير كلمات المرور (في التطبيق الحقيقي)
- تتبع محاولات تسجيل الدخول
- انتهاء الجلسة التلقائي
- حماية من التلاعب

## 🚀 المميزات

### 1. نظام المستخدمين
- تسجيل دخول وإنشاء حسابات
- مستويات مختلفة (عادي، VIP، أدمن)
- تتبع الأنشطة والإنجازات

### 2. نظام المهام
- مهام متنوعة (اجتماعية، فيديو، مواقع)
- تتبع الوقت المطلوب
- استهداف مستخدمين محددين
- مهام يومية

### 3. نظام الأكواد
- أكواد متنوعة بدرجات نادرة
- أكواد محدودة الوقت
- أكواد خاصة بالأدمن
- تتبع الاستخدام

### 4. نظام السحب
- سحب روبكس
- جيم باس مجاني
- تتبع حالة الطلبات
- متطلبات خاصة

### 5. نظام الدعوات
- روابط دعوة مخصصة
- مستويات مكافآت
- عمولة مدى الحياة
- مسابقات شهرية

## 📈 الإحصائيات

### إحصائيات عامة:
- إجمالي المستخدمين
- المستخدمين النشطين
- الروبكس الموزع
- المهام المكتملة

### إحصائيات يومية/أسبوعية/شهرية:
- مستخدمين جدد
- مهام مكتملة
- أكواد مستخدمة
- طلبات سحب

## 🛠️ التطوير

### إضافة بيانات جديدة:
1. قم بتحديث الملف JSON المناسب
2. أعد تحميل البيانات: `db.refresh()`
3. تحقق من التحديثات في الواجهة

### إضافة ميزات جديدة:
1. حدث `database.js` لإضافة دوال جديدة
2. حدث ملفات JSON لإضافة بيانات جديدة
3. حدث الواجهة لعرض البيانات الجديدة

## 🔄 النسخ الاحتياطي

البيانات محفوظة في:
- ملفات JSON (قاعدة البيانات الرئيسية)
- localStorage (نسخة احتياطية محلية)
- ذاكرة التخزين المؤقت (للأداء)

## 📝 ملاحظات مهمة

1. **هذه قاعدة بيانات مؤقتة** للعرض والاختبار
2. **في التطبيق الحقيقي** ستحتاج لقاعدة بيانات حقيقية (MySQL, MongoDB, إلخ)
3. **كلمات المرور** يجب تشفيرها في التطبيق الحقيقي
4. **البيانات الحساسة** يجب حمايتها بشكل أفضل
5. **النسخ الاحتياطي** ضروري للبيانات المهمة

## 🎯 التحسينات المستقبلية

- [ ] قاعدة بيانات حقيقية
- [ ] تشفير البيانات
- [ ] API للتفاعل مع الخادم
- [ ] نسخ احتياطي تلقائي
- [ ] مراقبة الأداء
- [ ] تحليلات متقدمة

---

**تم إنشاؤه بواسطة فريق Rix** 🎮✨
