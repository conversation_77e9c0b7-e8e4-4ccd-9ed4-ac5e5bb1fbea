<?php
/**
 * إضافة مهام تجريبية للموقع
 * Sample Tasks for Rix Website
 */

session_start();
require_once 'includes/db.php';

// التحقق من صلاحيات الأدمن
if (!is_admin()) {
    die('Access denied - Admin only');
}

// مصفوفة المهام التجريبية
$sample_tasks = [
    [
        'title' => '🎥 مشاهدة فيديو ترحيبي',
        'description' => 'شاهد الفيديو الترحيبي للموقع لمدة دقيقة واحدة واحصل على نقاط مجانية',
        'reward' => 15
    ],
    [
        'title' => '👍 إعجاب بصفحة الفيسبوك',
        'description' => 'سجل إعجابك بصفحتنا الرسمية على الفيسبوك وادعم المجتمع',
        'reward' => 25
    ],
    [
        'title' => '📱 تحميل التطبيق المجاني',
        'description' => 'حمل تطبيقنا المجاني من متجر التطبيقات واستمتع بالمزيد من المميزات',
        'reward' => 50
    ],
    [
        'title' => '🔔 تفعيل الإشعارات',
        'description' => 'فعل الإشعارات لتكون أول من يعلم بالمهام الجديدة والعروض الخاصة',
        'reward' => 20
    ],
    [
        'title' => '📝 إكمال الملف الشخصي',
        'description' => 'أكمل معلومات ملفك الشخصي لتحسين تجربتك على الموقع',
        'reward' => 30
    ],
    [
        'title' => '🎮 انضمام لجروب Roblox',
        'description' => 'انضم إلى جروبنا الرسمي في Roblox للحصول على المزيد من الفرص',
        'reward' => 40
    ],
    [
        'title' => '⭐ تقييم الموقع',
        'description' => 'قيم تجربتك على موقعنا وساعدنا في التحسين',
        'reward' => 35
    ],
    [
        'title' => '📢 مشاركة مع الأصدقاء',
        'description' => 'شارك رابط الموقع مع أصدقائك وساعدهم في ربح الروبكس أيضاً',
        'reward' => 45
    ],
    [
        'title' => '📺 مشاهدة إعلان قصير',
        'description' => 'شاهد إعلان قصير لمدة 30 ثانية لدعم الموقع',
        'reward' => 10
    ],
    [
        'title' => '🎯 إكمال الاستبيان',
        'description' => 'أكمل استبيان قصير عن اهتماماتك في الألعاب (5 دقائق)',
        'reward' => 60
    ],
    [
        'title' => '📸 متابعة الإنستغرام',
        'description' => 'تابع حسابنا على الإنستغرام للحصول على آخر الأخبار والمسابقات',
        'reward' => 25
    ],
    [
        'title' => '🎵 الاشتراك في قناة يوتيوب',
        'description' => 'اشترك في قناتنا على يوتيوب وفعل الجرس للإشعارات',
        'reward' => 55
    ],
    [
        'title' => '💬 انضمام لخادم Discord',
        'description' => 'انضم إلى خادم Discord الخاص بنا للدردشة مع المجتمع',
        'reward' => 35
    ],
    [
        'title' => '🔄 مشاركة منشور تويتر',
        'description' => 'أعد تغريد منشورنا الأخير على تويتر لنشر الكلمة',
        'reward' => 30
    ],
    [
        'title' => '📧 الاشتراك في النشرة البريدية',
        'description' => 'اشترك في نشرتنا البريدية لتصلك آخر العروض والمهام الجديدة',
        'reward' => 25
    ],
    [
        'title' => '🎪 لعب لعبة مصغرة',
        'description' => 'العب إحدى ألعابنا المصغرة واحصل على نقاط إضافية',
        'reward' => 40
    ],
    [
        'title' => '📝 كتابة تعليق إيجابي',
        'description' => 'اكتب تعليق إيجابي عن تجربتك مع الموقع',
        'reward' => 35
    ],
    [
        'title' => '🎁 فتح صندوق المكافآت اليومي',
        'description' => 'افتح صندوق المكافآت اليومي واحصل على مفاجأة',
        'reward' => 20
    ],
    [
        'title' => '🏆 إكمال التحدي الأسبوعي',
        'description' => 'أكمل التحدي الأسبوعي واحصل على مكافأة كبيرة',
        'reward' => 100
    ],
    [
        'title' => '🎨 تخصيص الصورة الشخصية',
        'description' => 'ارفع صورة شخصية مميزة لحسابك',
        'reward' => 15
    ]
];

$message = '';
$error = '';

// معالجة إضافة المهام
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_sample_tasks'])) {
    $added_count = 0;
    $skipped_count = 0;
    
    foreach ($sample_tasks as $task) {
        // التحقق من عدم وجود المهمة مسبقاً
        $stmt = $pdo->prepare("SELECT id FROM tasks WHERE title = ?");
        $stmt->execute([$task['title']]);
        
        if (!$stmt->fetch()) {
            $stmt = $pdo->prepare("INSERT INTO tasks (title, description, reward, is_active) VALUES (?, ?, ?, 1)");
            if ($stmt->execute([$task['title'], $task['description'], $task['reward']])) {
                $added_count++;
            }
        } else {
            $skipped_count++;
        }
    }
    
    $message = "تم إضافة {$added_count} مهمة جديدة. تم تخطي {$skipped_count} مهمة موجودة مسبقاً.";
}

// معالجة حذف جميع المهام
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['clear_all_tasks'])) {
    $stmt = $pdo->prepare("DELETE FROM tasks");
    if ($stmt->execute()) {
        $message = "تم حذف جميع المهام بنجاح";
    } else {
        $error = "حدث خطأ أثناء حذف المهام";
    }
}

// جلب المهام الحالية
$stmt = $pdo->prepare("SELECT COUNT(*) as task_count FROM tasks");
$stmt->execute();
$current_task_count = $stmt->fetch()['task_count'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهام التجريبية - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="admin.php">لوحة الأدمن</a>
                    <a href="dashboard.php">لوحة التحكم</a>
                    <a href="index.php">الرئيسية</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="admin-section">
            <div class="container">
                <div class="admin-header">
                    <h1>🎯 إدارة المهام التجريبية</h1>
                    <p>إضافة وإدارة المهام التجريبية للموقع</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        ✅ <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        ❌ <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <div class="admin-section-card">
                    <h3>📊 معلومات المهام</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📋</div>
                            <div class="stat-number"><?php echo $current_task_count; ?></div>
                            <div class="stat-label">المهام الحالية</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🆕</div>
                            <div class="stat-number"><?php echo count($sample_tasks); ?></div>
                            <div class="stat-label">المهام التجريبية المتاحة</div>
                        </div>
                    </div>
                </div>

                <div class="admin-section-card">
                    <h3>⚡ إجراءات سريعة</h3>
                    <div class="actions-grid">
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="add_sample_tasks" class="btn btn-primary"
                                    onclick="return confirm('هل تريد إضافة جميع المهام التجريبية؟')">
                                ➕ إضافة المهام التجريبية
                            </button>
                        </form>
                        
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="clear_all_tasks" class="btn btn-danger"
                                    onclick="return confirm('تحذير: سيتم حذف جميع المهام نهائياً. هل أنت متأكد؟')">
                                🗑️ حذف جميع المهام
                            </button>
                        </form>
                    </div>
                </div>

                <div class="admin-section-card">
                    <h3>📋 قائمة المهام التجريبية</h3>
                    <div class="tasks-preview">
                        <?php foreach ($sample_tasks as $index => $task): ?>
                            <div class="task-preview-card">
                                <div class="task-preview-header">
                                    <h4><?php echo htmlspecialchars($task['title']); ?></h4>
                                    <span class="task-reward"><?php echo $task['reward']; ?> روبكس</span>
                                </div>
                                <p class="task-preview-description">
                                    <?php echo htmlspecialchars($task['description']); ?>
                                </p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="admin-section-card">
                    <h3>💡 نصائح مهمة</h3>
                    <div class="tips-list">
                        <div class="tip-item">
                            <strong>🔄 التحديث:</strong> يمكنك إضافة المهام التجريبية عدة مرات، المهام المكررة سيتم تخطيها تلقائياً.
                        </div>
                        <div class="tip-item">
                            <strong>⚠️ الحذف:</strong> حذف جميع المهام سيؤثر على المستخدمين الذين أكملوا مهام، استخدم بحذر.
                        </div>
                        <div class="tip-item">
                            <strong>✏️ التعديل:</strong> يمكنك تعديل المهام بعد إضافتها من لوحة الأدمن الرئيسية.
                        </div>
                        <div class="tip-item">
                            <strong>🎯 التخصيص:</strong> يمكنك تعديل هذا الملف لإضافة مهام مخصصة حسب احتياجاتك.
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <style>
        .task-preview-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .task-preview-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .task-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .task-preview-header h4 {
            margin: 0;
            color: #333;
            flex: 1;
        }
        
        .task-reward {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .task-preview-description {
            color: #6c757d;
            margin: 0;
            line-height: 1.6;
        }
        
        .tips-list {
            display: grid;
            gap: 1rem;
        }
        
        .tip-item {
            background: #e7f3ff;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
    </style>

    <script src="assets/script.js"></script>
</body>
</html>
