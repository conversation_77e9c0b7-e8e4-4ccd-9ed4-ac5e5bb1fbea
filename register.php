<?php
session_start();
require_once 'includes/db.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (is_logged_in()) {
    header('Location: dashboard.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = clean_input($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($password) || empty($confirm_password)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (strlen($username) < 3) {
        $error = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمات المرور غير متطابقة';
    } else {
        // التحقق من عدم وجود اسم المستخدم
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        
        if ($stmt->fetch()) {
            $error = 'اسم المستخدم موجود بالفعل';
        } else {
            // إنشاء الحساب
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // التحقق من أول مستخدم (سيكون أدمن)
            $stmt = $pdo->prepare("SELECT COUNT(*) as user_count FROM users");
            $stmt->execute();
            $user_count = $stmt->fetch()['user_count'];
            $is_admin = ($user_count == 0) ? 1 : 0;
            
            $stmt = $pdo->prepare("INSERT INTO users (username, password, is_admin) VALUES (?, ?, ?)");
            
            if ($stmt->execute([$username, $hashed_password, $is_admin])) {
                $success = 'تم إنشاء الحساب بنجاح! يمكنك تسجيل الدخول الآن';
                if ($is_admin) {
                    $success .= ' (تم منحك صلاحيات الأدمن كأول مستخدم)';
                }
            } else {
                $error = 'حدث خطأ أثناء إنشاء الحساب';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="login.php">تسجيل الدخول</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="auth-section">
            <div class="container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h2>🚀 إنشاء حساب جديد</h2>
                        <p>انضم إلى Rix واربح روبكس مجاناً</p>
                    </div>

                    <?php if ($error): ?>
                        <div class="alert alert-error">
                            ❌ <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo $success; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="auth-form">
                        <div class="form-group">
                            <label for="username">اسم المستخدم</label>
                            <input type="text" id="username" name="username" required 
                                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                                   placeholder="اختر اسم مستخدم فريد">
                            <small>يجب أن يكون 3 أحرف على الأقل</small>
                        </div>

                        <div class="form-group">
                            <label for="password">كلمة المرور</label>
                            <input type="password" id="password" name="password" required 
                                   placeholder="اختر كلمة مرور قوية">
                            <small>يجب أن تكون 6 أحرف على الأقل</small>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">تأكيد كلمة المرور</label>
                            <input type="password" id="confirm_password" name="confirm_password" required 
                                   placeholder="أعد كتابة كلمة المرور">
                        </div>

                        <button type="submit" class="btn btn-primary btn-full">
                            إنشاء الحساب 🎯
                        </button>
                    </form>

                    <div class="auth-footer">
                        <p>لديك حساب بالفعل؟ <a href="login.php">سجل دخولك هنا</a></p>
                    </div>

                    <div class="features-preview">
                        <h3>ماذا ستحصل عليه؟</h3>
                        <ul>
                            <li>✅ مهام سهلة ومربحة</li>
                            <li>✅ روبكس حقيقي مضمون</li>
                            <li>✅ دفع خلال 24 ساعة</li>
                            <li>✅ موقع آمن 100%</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
</body>
</html>
