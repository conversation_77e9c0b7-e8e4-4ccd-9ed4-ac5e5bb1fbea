// نظام المهام المتطور
class TasksSystem {
    constructor() {
        this.tasks = [];
        this.dailyTasks = {};
        this.quizQuestions = [];
        this.userProgress = {
            completedTasks: 0,
            streakDays: 1,
            todayEarnings: 0,
            level: 1,
            achievements: []
        };
        this.init();
    }

    init() {
        this.checkLoginStatus();
        this.loadUserProgress();
        this.generateTasks();
        this.generateQuizQuestions();
        this.setupEventListeners();
        this.updateUI();
        this.startTimers();
    }

    checkLoginStatus() {
        if (!rix.currentUser) {
            window.location.href = 'index.html';
            return;
        }
    }

    loadUserProgress() {
        const saved = localStorage.getItem('userProgress_' + rix.currentUser.username);
        if (saved) {
            this.userProgress = { ...this.userProgress, ...JSON.parse(saved) };
        }
        
        // تحديث المهام اليومية
        const today = new Date().toDateString();
        const lastVisit = localStorage.getItem('lastVisit_' + rix.currentUser.username);
        
        if (lastVisit !== today) {
            this.resetDailyTasks();
            localStorage.setItem('lastVisit_' + rix.currentUser.username, today);
        }
    }

    saveUserProgress() {
        localStorage.setItem('userProgress_' + rix.currentUser.username, JSON.stringify(this.userProgress));
    }

    generateTasks() {
        // تحميل المهام من الأدمن
        this.loadAdminTasks();

        // المهام الافتراضية
        const defaultTasks = [
            // مهام وسائل التواصل
            {
                id: 1,
                title: 'تابع صفحتنا على فيسبوك',
                description: 'تابع صفحة Rix الرسمية على فيسبوك واحصل على آخر الأخبار',
                reward: 25,
                type: 'social',
                icon: 'fab fa-facebook',
                link: 'https://facebook.com/rix',
                difficulty: 'easy',
                timeRequired: '1 دقيقة'
            },
            {
                id: 2,
                title: 'انضم لقناة التليجرام',
                description: 'انضم لقناة Rix على التليجرام للحصول على أكواد حصرية',
                reward: 30,
                type: 'social',
                icon: 'fab fa-telegram',
                link: 'https://t.me/rix_official',
                difficulty: 'easy',
                timeRequired: '1 دقيقة'
            },
            {
                id: 3,
                title: 'تابعنا على تويتر',
                description: 'تابع حساب Rix على تويتر وأعد تغريد المنشور المثبت',
                reward: 35,
                type: 'social',
                icon: 'fab fa-twitter',
                link: 'https://twitter.com/rix_official',
                difficulty: 'medium',
                timeRequired: '2 دقيقة'
            },
            
            // مهام الفيديو
            {
                id: 4,
                title: 'شاهد فيديو تعريفي عن Roblox',
                description: 'شاهد فيديو مدته 5 دقائق عن أساسيات لعبة Roblox',
                reward: 40,
                type: 'video',
                icon: 'fas fa-play-circle',
                link: 'https://youtube.com/watch?v=example',
                difficulty: 'easy',
                timeRequired: '5 دقائق'
            },
            {
                id: 5,
                title: 'شاهد دليل ربح الروبكس',
                description: 'تعلم أفضل الطرق لربح الروبكس من خلال هذا الفيديو التعليمي',
                reward: 50,
                type: 'video',
                icon: 'fas fa-graduation-cap',
                link: 'https://youtube.com/watch?v=example2',
                difficulty: 'medium',
                timeRequired: '8 دقائق'
            },
            
            // مهام التطبيقات
            {
                id: 6,
                title: 'حمل تطبيق Roblox',
                description: 'حمل تطبيق Roblox الرسمي من متجر التطبيقات',
                reward: 45,
                type: 'app',
                icon: 'fas fa-download',
                link: 'https://play.google.com/store/apps/details?id=com.roblox.client',
                difficulty: 'easy',
                timeRequired: '3 دقائق'
            },
            {
                id: 7,
                title: 'جرب تطبيق الألعاب المجانية',
                description: 'حمل وجرب تطبيق ألعاب مجانية لمدة 5 دقائق',
                reward: 60,
                type: 'app',
                icon: 'fas fa-gamepad',
                link: 'https://example-game-app.com',
                difficulty: 'medium',
                timeRequired: '10 دقائق'
            },
            
            // مهام الاستبيانات
            {
                id: 8,
                title: 'استبيان عن ألعابك المفضلة',
                description: 'أجب على استبيان قصير عن ألعابك المفضلة في Roblox',
                reward: 35,
                type: 'survey',
                icon: 'fas fa-clipboard-list',
                link: '#survey1',
                difficulty: 'easy',
                timeRequired: '3 دقائق'
            },
            {
                id: 9,
                title: 'استبيان تجربة المستخدم',
                description: 'ساعدنا في تحسين موقع Rix من خلال استبيان سريع',
                reward: 40,
                type: 'survey',
                icon: 'fas fa-star',
                link: '#survey2',
                difficulty: 'easy',
                timeRequired: '4 دقائق'
            },
            
            // مهام الألعاب
            {
                id: 10,
                title: 'العب لعبة الذاكرة',
                description: 'العب لعبة الذاكرة واحصل على نتيجة أعلى من 80%',
                reward: 55,
                type: 'game',
                icon: 'fas fa-brain',
                link: '#memory-game',
                difficulty: 'medium',
                timeRequired: '5 دقائق'
            },
            {
                id: 11,
                title: 'لعبة الأسئلة السريعة',
                description: 'أجب على 10 أسئلة سريعة عن Roblox',
                reward: 45,
                type: 'game',
                icon: 'fas fa-question-circle',
                link: '#quick-quiz',
                difficulty: 'medium',
                timeRequired: '4 دقائق'
            },
            {
                id: 12,
                title: 'تحدي الرياضيات',
                description: 'حل 15 مسألة رياضية بسيطة في أقل من 5 دقائق',
                reward: 65,
                type: 'game',
                icon: 'fas fa-calculator',
                link: '#math-challenge',
                difficulty: 'hard',
                timeRequired: '5 دقائق'
            }
        ];

        // دمج المهام الافتراضية مع مهام الأدمن
        this.tasks = [...defaultTasks, ...this.adminTasks];
    }

    loadAdminTasks() {
        this.adminTasks = [];
        const adminData = localStorage.getItem('adminData');

        if (adminData) {
            const data = JSON.parse(adminData);
            const allAdminTasks = data.adminTasks || [];

            // فلترة المهام حسب المستخدم
            allAdminTasks.forEach(task => {
                if (!task.isActive) return;

                // التحقق من استهداف المهمة
                let shouldInclude = false;

                if (task.target === 'all') {
                    shouldInclude = true;
                } else if (task.target === 'specific' && task.specificUser === rix.currentUser.username) {
                    shouldInclude = true;
                } else if (task.target === 'level' && this.userProgress.level >= (task.requiredLevel || 1)) {
                    shouldInclude = true;
                }

                // التحقق من عدم إكمال المهمة من قبل
                if (shouldInclude && !task.completedBy?.includes(rix.currentUser.username)) {
                    // تحويل مهمة الأدمن لتنسيق المهام العادية
                    const adminTask = {
                        id: task.id,
                        title: task.title,
                        description: task.description,
                        reward: task.reward,
                        type: task.type,
                        icon: this.getTaskIcon(task.type),
                        link: task.link,
                        difficulty: task.difficulty || 'medium',
                        timeRequired: this.formatDuration(task.duration || 60),
                        duration: task.duration || 60,
                        requireLogin: task.requireLogin !== false,
                        trackTime: task.trackTime !== false,
                        isAdminTask: true
                    };

                    this.adminTasks.push(adminTask);
                }
            });
        }
    }

    getTaskIcon(type) {
        const icons = {
            'custom': 'fas fa-cog',
            'social': 'fab fa-facebook',
            'video': 'fas fa-play-circle',
            'app': 'fas fa-mobile-alt',
            'group': 'fas fa-users',
            'survey': 'fas fa-clipboard-list',
            'website': 'fas fa-globe'
        };
        return icons[type] || 'fas fa-tasks';
    }

    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds} ثانية`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            return `${minutes} دقيقة`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours} ساعة ${minutes > 0 ? `و ${minutes} دقيقة` : ''}`;
        }
    }

    generateQuizQuestions() {
        this.quizQuestions = [
            {
                question: 'ما هو اسم العملة الرسمية في لعبة Roblox؟',
                options: ['روبكس', 'كوينز', 'جيمز', 'بوينتس'],
                correct: 0
            },
            {
                question: 'في أي عام تم إطلاق لعبة Roblox؟',
                options: ['2004', '2006', '2008', '2010'],
                correct: 1
            },
            {
                question: 'ما هو الحد الأقصى لعدد الأصدقاء في Roblox؟',
                options: ['100', '200', '500', '1000'],
                correct: 1
            },
            {
                question: 'ما هو اسم محرر الألعاب في Roblox؟',
                options: ['Roblox Studio', 'Game Maker', 'Roblox Builder', 'Studio Pro'],
                correct: 0
            },
            {
                question: 'كم يكلف Roblox Premium الشهري الأساسي؟',
                options: ['4.99$', '9.99$', '19.99$', '29.99$'],
                correct: 0
            }
        ];
    }

    setupEventListeners() {
        // فلتر المهام
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.filterTasks(e.target.getAttribute('data-filter'));
            });
        });
    }

    updateUI() {
        // تحديث الإحصائيات
        document.getElementById('completedTasks').textContent = this.userProgress.completedTasks;
        document.getElementById('streakDays').textContent = this.userProgress.streakDays;
        document.getElementById('todayEarnings').textContent = this.userProgress.todayEarnings;
        document.getElementById('userLevel').textContent = this.userProgress.level;
        document.getElementById('userBalance').textContent = `${rix.userBalance} روبكس`;

        // تحديث شبكة المهام
        this.renderTasks();
        
        // تحديث حالة المهام اليومية
        this.updateDailyTasksStatus();
    }

    renderTasks() {
        const tasksGrid = document.getElementById('tasksGrid');
        if (!tasksGrid) return;

        tasksGrid.innerHTML = this.tasks.map(task => `
            <div class="task-card ${task.type}" data-type="${task.type}">
                <div class="task-header">
                    <div class="task-icon">
                        <i class="${task.icon}"></i>
                    </div>
                    <div class="task-difficulty ${task.difficulty}">
                        ${this.getDifficultyText(task.difficulty)}
                    </div>
                </div>
                <div class="task-content">
                    <h3>${task.title}</h3>
                    <p>${task.description}</p>
                    <div class="task-meta">
                        <span class="task-time">
                            <i class="fas fa-clock"></i> ${task.timeRequired}
                        </span>
                        <span class="task-reward">
                            <i class="fas fa-coins"></i> +${task.reward} روبكس
                        </span>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-primary" onclick="tasksSystem.startTask(${task.id})">
                        <i class="fas fa-play"></i> ابدأ المهمة
                    </button>
                </div>
            </div>
        `).join('');
    }

    getDifficultyText(difficulty) {
        const difficulties = {
            'easy': 'سهل',
            'medium': 'متوسط',
            'hard': 'صعب'
        };
        return difficulties[difficulty] || 'غير محدد';
    }

    filterTasks(filter) {
        const taskCards = document.querySelectorAll('.task-card');
        taskCards.forEach(card => {
            if (filter === 'all' || card.classList.contains(filter)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    startTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;

        // التحقق من المتطلبات
        if (task.requireLogin && !rix.currentUser) {
            rix.showErrorMessage('يجب تسجيل الدخول لإكمال هذه المهمة');
            return;
        }

        // فتح رابط المهمة
        if (task.link && task.link.startsWith('http')) {
            const taskWindow = window.open(task.link, '_blank');

            if (task.trackTime && task.duration) {
                this.startTimeTracking(taskId, taskWindow, task.duration);
            } else {
                // محاكاة إكمال المهمة بعد وقت قصير للمهام بدون تتبع
                setTimeout(() => {
                    this.completeTask(taskId);
                }, 3000);
            }
        } else {
            // مهمة بدون رابط
            setTimeout(() => {
                this.completeTask(taskId);
            }, 3000);
        }

        rix.showSuccessMessage(`تم بدء المهمة: ${task.title}`);
    }

    startTimeTracking(taskId, taskWindow, requiredDuration) {
        const startTime = Date.now();
        let timeSpent = 0;

        // إنشاء نافذة تتبع الوقت
        const trackingModal = this.createTrackingModal(taskId, requiredDuration);
        document.body.appendChild(trackingModal);

        const trackingInterval = setInterval(() => {
            // التحقق من إغلاق النافذة
            if (taskWindow.closed) {
                clearInterval(trackingInterval);
                trackingModal.remove();

                timeSpent = Math.floor((Date.now() - startTime) / 1000);

                if (timeSpent >= requiredDuration) {
                    this.completeTask(taskId);
                    rix.showSuccessMessage(`تم إكمال المهمة بنجاح! قضيت ${timeSpent} ثانية`);
                } else {
                    rix.showErrorMessage(`لم تقضِ الوقت المطلوب. قضيت ${timeSpent} ثانية من أصل ${requiredDuration} ثانية مطلوبة`);
                }
                return;
            }

            timeSpent = Math.floor((Date.now() - startTime) / 1000);
            this.updateTrackingModal(trackingModal, timeSpent, requiredDuration);

            // إكمال تلقائي عند الوصول للوقت المطلوب
            if (timeSpent >= requiredDuration) {
                clearInterval(trackingInterval);
                taskWindow.close();
                trackingModal.remove();
                this.completeTask(taskId);
                rix.showSuccessMessage(`تم إكمال المهمة بنجاح! قضيت ${timeSpent} ثانية`);
            }
        }, 1000);
    }

    createTrackingModal(taskId, requiredDuration) {
        const modal = document.createElement('div');
        modal.className = 'time-tracking-modal';
        modal.innerHTML = `
            <div class="tracking-content">
                <h3><i class="fas fa-clock"></i> تتبع وقت المهمة</h3>
                <div class="time-display">
                    <div class="time-spent">0</div>
                    <div class="time-required">من أصل ${requiredDuration} ثانية</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <p>ابق في الصفحة المفتوحة لإكمال المهمة</p>
                <button class="btn btn-danger btn-sm" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        `;
        return modal;
    }

    updateTrackingModal(modal, timeSpent, requiredDuration) {
        const timeDisplay = modal.querySelector('.time-spent');
        const progressFill = modal.querySelector('.progress-fill');

        if (timeDisplay) {
            timeDisplay.textContent = timeSpent;
        }

        if (progressFill) {
            const progress = Math.min((timeSpent / requiredDuration) * 100, 100);
            progressFill.style.width = progress + '%';
        }
    }

    completeTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;

        // إضافة المكافأة
        rix.userBalance += task.reward;
        this.userProgress.completedTasks++;
        this.userProgress.todayEarnings += task.reward;

        // تحديث المستوى
        this.updateLevel();

        // إذا كانت مهمة أدمن، تحديث بيانات الأدمن
        if (task.isAdminTask) {
            this.updateAdminTaskCompletion(taskId);
        }

        // حفظ البيانات
        rix.saveUserData();
        this.saveUserProgress();

        // تحديث الواجهة
        this.updateUI();

        rix.showSuccessMessage(`تم إكمال المهمة! حصلت على ${task.reward} روبكس`);

        // إزالة المهمة من القائمة
        this.tasks = this.tasks.filter(t => t.id !== taskId);
        this.renderTasks();
    }

    updateAdminTaskCompletion(taskId) {
        const adminData = localStorage.getItem('adminData');
        if (adminData) {
            const data = JSON.parse(adminData);
            const adminTask = data.adminTasks?.find(t => t.id === taskId);

            if (adminTask) {
                if (!adminTask.completedBy) {
                    adminTask.completedBy = [];
                }

                if (!adminTask.completedBy.includes(rix.currentUser.username)) {
                    adminTask.completedBy.push(rix.currentUser.username);
                    adminTask.totalCompletions = (adminTask.totalCompletions || 0) + 1;

                    localStorage.setItem('adminData', JSON.stringify(data));
                }
            }
        }
    }

    updateLevel() {
        const newLevel = Math.floor(this.userProgress.completedTasks / 10) + 1;
        if (newLevel > this.userProgress.level) {
            this.userProgress.level = newLevel;
            rix.showSuccessMessage(`تهانينا! وصلت للمستوى ${newLevel}! 🎉`);
        }
    }

    resetDailyTasks() {
        this.dailyTasks = {
            login: false,
            share: false,
            ads: false,
            quiz: false
        };
        localStorage.setItem('dailyTasks_' + rix.currentUser.username, JSON.stringify(this.dailyTasks));
    }

    updateDailyTasksStatus() {
        const saved = localStorage.getItem('dailyTasks_' + rix.currentUser.username);
        if (saved) {
            this.dailyTasks = JSON.parse(saved);
        }

        // تحديث حالة الأزرار
        Object.keys(this.dailyTasks).forEach(taskType => {
            const button = document.querySelector(`#daily${taskType.charAt(0).toUpperCase() + taskType.slice(1)} button`);
            if (button && this.dailyTasks[taskType]) {
                button.innerHTML = '<i class="fas fa-check"></i> مكتمل';
                button.disabled = true;
                button.classList.remove('btn-success', 'btn-primary', 'btn-warning', 'btn-info');
                button.classList.add('btn-secondary');
            }
        });
    }

    startTimers() {
        // تحديث مؤقت التحدي الأسبوعي
        setInterval(() => {
            this.updateChallengeTimer();
        }, 1000);
    }

    updateChallengeTimer() {
        const now = new Date();
        const nextWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - now.getDay()));
        const diff = nextWeek - now;
        
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        
        const timerElement = document.getElementById('challengeTimer');
        if (timerElement) {
            timerElement.textContent = `${days} أيام و ${hours} ساعة و ${minutes} دقيقة متبقية`;
        }
    }
}

// دوال المهام اليومية
function claimDailyLogin() {
    if (tasksSystem.dailyTasks.login) {
        rix.showErrorMessage('لقد استلمت مكافأة تسجيل الدخول اليوم');
        return;
    }

    rix.userBalance += 15;
    tasksSystem.userProgress.todayEarnings += 15;
    tasksSystem.dailyTasks.login = true;
    
    rix.saveUserData();
    tasksSystem.saveUserProgress();
    localStorage.setItem('dailyTasks_' + rix.currentUser.username, JSON.stringify(tasksSystem.dailyTasks));
    
    tasksSystem.updateUI();
    tasksSystem.updateDailyTasksStatus();
    
    rix.showSuccessMessage('تم استلام مكافأة تسجيل الدخول! +15 روبكس');
}

function shareDaily() {
    if (tasksSystem.dailyTasks.share) {
        rix.showErrorMessage('لقد شاركت الموقع اليوم');
        return;
    }

    const shareUrl = window.location.origin + '?ref=' + rix.currentUser.username;
    
    if (navigator.share) {
        navigator.share({
            title: 'Rix - اربح روبكس مجاناً',
            text: 'انضم لموقع Rix واربح روبكس حقيقي مجاناً!',
            url: shareUrl
        });
    } else {
        navigator.clipboard.writeText(shareUrl);
        rix.showSuccessMessage('تم نسخ رابط المشاركة!');
    }

    rix.userBalance += 20;
    tasksSystem.userProgress.todayEarnings += 20;
    tasksSystem.dailyTasks.share = true;
    
    rix.saveUserData();
    tasksSystem.saveUserProgress();
    localStorage.setItem('dailyTasks_' + rix.currentUser.username, JSON.stringify(tasksSystem.dailyTasks));
    
    tasksSystem.updateUI();
    tasksSystem.updateDailyTasksStatus();
    
    rix.showSuccessMessage('تم مشاركة الموقع! +20 روبكس');
}

function watchAds() {
    if (tasksSystem.dailyTasks.ads) {
        rix.showErrorMessage('لقد شاهدت الإعلانات اليوم');
        return;
    }

    // فتح نافذة الإعلان
    document.getElementById('adModal').style.display = 'block';
    
    let timeLeft = 30;
    const timer = setInterval(() => {
        document.getElementById('adTimer').textContent = timeLeft;
        timeLeft--;
        
        if (timeLeft < 0) {
            clearInterval(timer);
            document.getElementById('claimAdReward').style.display = 'block';
        }
    }, 1000);
}

function claimAdReward() {
    rix.userBalance += 25;
    tasksSystem.userProgress.todayEarnings += 25;
    tasksSystem.dailyTasks.ads = true;
    
    rix.saveUserData();
    tasksSystem.saveUserProgress();
    localStorage.setItem('dailyTasks_' + rix.currentUser.username, JSON.stringify(tasksSystem.dailyTasks));
    
    tasksSystem.updateUI();
    tasksSystem.updateDailyTasksStatus();
    
    closeModal('adModal');
    rix.showSuccessMessage('تم مشاهدة الإعلانات! +25 روبكس');
}

function startQuiz() {
    if (tasksSystem.dailyTasks.quiz) {
        rix.showErrorMessage('لقد أكملت الاختبار اليوم');
        return;
    }

    // فتح نافذة الاختبار
    document.getElementById('quizModal').style.display = 'block';
    
    // عرض الأسئلة
    const quizContent = document.getElementById('quizContent');
    let currentQuestion = 0;
    let score = 0;
    
    function showQuestion() {
        const question = tasksSystem.quizQuestions[currentQuestion];
        quizContent.innerHTML = `
            <div class="quiz-question">
                <h3>السؤال ${currentQuestion + 1} من ${tasksSystem.quizQuestions.length}</h3>
                <p>${question.question}</p>
                <div class="quiz-options">
                    ${question.options.map((option, index) => `
                        <button class="quiz-option" onclick="selectAnswer(${index})">${option}</button>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    window.selectAnswer = function(selectedIndex) {
        const question = tasksSystem.quizQuestions[currentQuestion];
        if (selectedIndex === question.correct) {
            score++;
        }
        
        currentQuestion++;
        
        if (currentQuestion < tasksSystem.quizQuestions.length) {
            showQuestion();
        } else {
            // انتهاء الاختبار
            const reward = score >= 3 ? 30 : 15;
            rix.userBalance += reward;
            tasksSystem.userProgress.todayEarnings += reward;
            tasksSystem.dailyTasks.quiz = true;
            
            rix.saveUserData();
            tasksSystem.saveUserProgress();
            localStorage.setItem('dailyTasks_' + rix.currentUser.username, JSON.stringify(tasksSystem.dailyTasks));
            
            tasksSystem.updateUI();
            tasksSystem.updateDailyTasksStatus();
            
            closeModal('quizModal');
            rix.showSuccessMessage(`انتهى الاختبار! النتيجة: ${score}/5 - حصلت على ${reward} روبكس`);
        }
    };
    
    showQuestion();
}

function logout() {
    rix.logout();
}

// إنشاء نسخة من نظام المهام
const tasksSystem = new TasksSystem();
