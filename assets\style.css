/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #fff;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* شريط التنقل */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.nav-brand h1 {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 700;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 5px rgba(255, 107, 107, 0.5)); }
    to { filter: drop-shadow(0 0 20px rgba(78, 205, 196, 0.8)); }
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #fff;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
    box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
}

.btn-warning {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* القسم الرئيسي */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding-top: 80px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

.hero-content {
    text-align: center;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
}

.gradient-text {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 150px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* الأشكال المتحركة */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* الأقسام */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features {
    padding: 5rem 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.feature-card p {
    opacity: 0.8;
    line-height: 1.6;
}

/* قسم المهام */
.tasks-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
}

.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.task-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-card:hover::before {
    opacity: 1;
}

.task-card:hover {
    transform: translateY(-5px);
    border-color: rgba(78, 205, 196, 0.5);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.task-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #fff;
}

.task-reward {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.task-description {
    opacity: 0.8;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.task-button {
    position: relative;
    z-index: 2;
}

.tasks-info {
    margin-top: 3rem;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-card h3 {
    margin-bottom: 1.5rem;
    color: #4ecdc4;
}

.info-card ul {
    list-style: none;
}

.info-card li {
    padding: 0.5rem 0;
    opacity: 0.9;
}

/* قسم السحب */
.withdraw-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.withdraw-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-bottom: 4rem;
}

.withdraw-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    text-align: center;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.withdraw-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.withdraw-card:hover::before {
    opacity: 1;
}

.withdraw-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.robux-card {
    border-top: 3px solid #ff6b6b;
}

.gamepass-card {
    border-top: 3px solid #4ecdc4;
}

.withdraw-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
}

.withdraw-card h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #fff;
    position: relative;
    z-index: 2;
}

.withdraw-details {
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.withdraw-details p {
    margin-bottom: 0.8rem;
    opacity: 0.9;
}

.withdraw-details strong {
    color: #4ecdc4;
}

.withdraw-requirements {
    margin-top: 4rem;
}

.withdraw-requirements h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #f093fb;
}

.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.requirement-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.requirement-item i {
    font-size: 2rem;
    color: #4ecdc4;
    margin-top: 0.5rem;
}

.requirement-item h4 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.requirement-item p {
    opacity: 0.8;
    line-height: 1.6;
}

/* كيف يعمل */
.how-it-works {
    padding: 5rem 0;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.step-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.step-content p {
    opacity: 0.8;
    line-height: 1.6;
}

/* التذييل */
.footer {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #4ecdc4;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #fff;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #4ecdc4;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

.footer-stats {
    display: flex;
    gap: 2rem;
}

.footer-stat {
    text-align: center;
}

.footer-stat .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.footer-stat .stat-label {
    font-size: 0.9rem;
    opacity: 0.7;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    margin: 5% auto;
    padding: 3rem;
    border-radius: 25px;
    width: 90%;
    max-width: 500px;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff6b6b;
}

.modal-content h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #fff;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.modal-footer {
    text-align: center;
    margin-top: 1.5rem;
    opacity: 0.8;
}

.modal-footer a {
    color: #4ecdc4;
    text-decoration: none;
    font-weight: 600;
}

.modal-footer a:hover {
    color: #ff6b6b;
}

/* لوحة التحكم */
.dashboard {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-balance {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    display: inline-block;
    font-weight: 600;
    font-size: 1.2rem;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dashboard-card h3 {
    margin-bottom: 1rem;
    color: #4ecdc4;
}

/* صفحة الأكواد */
.codes-section {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
}

.code-generator {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 3rem;
}

.code-input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.code-input-group input {
    flex: 1;
}

.generated-code {
    background: rgba(78, 205, 196, 0.1);
    border: 2px solid #4ecdc4;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: #4ecdc4;
    margin-top: 1rem;
    word-break: break-all;
}

.codes-list {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.code-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.code-item:last-child {
    border-bottom: none;
}

.code-details {
    flex: 1;
}

.code-value {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    color: #4ecdc4;
    font-size: 1.1rem;
}

.code-info {
    font-size: 0.9rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.code-actions {
    display: flex;
    gap: 0.5rem;
}

/* صفحة الدعوات */
.invite-section {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.invite-link-generator {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    margin-bottom: 3rem;
}

.invite-link {
    background: rgba(78, 205, 196, 0.1);
    border: 2px solid #4ecdc4;
    padding: 1rem;
    border-radius: 15px;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    color: #4ecdc4;
    margin: 1rem 0;
    word-break: break-all;
}

.invite-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.invite-stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.invite-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.invite-stat-label {
    opacity: 0.8;
    margin-top: 0.5rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid,
    .tasks-grid,
    .withdraw-options,
    .requirements-grid,
    .steps-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 10% auto;
        padding: 2rem;
    }

    .code-input-group {
        flex-direction: column;
    }

    .code-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .code-actions {
        width: 100%;
        justify-content: center;
    }
}

/* تأثيرات إضافية */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.shake {
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.success-message {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    margin: 1rem 0;
    animation: slideIn 0.3s ease;
}

.error-message {
    background: linear-gradient(45deg, #ff6b6b, #f5576c);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    margin: 1rem 0;
    animation: shake 0.5s ease;
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: #fff;
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.loading-logo i {
    font-size: 3rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-logo h1 {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(78, 205, 196, 0.3);
    border-top: 3px solid #4ecdc4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-progress {
    width: 100%;
    max-width: 300px;
    margin-top: 2rem;
}

.loading-progress .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.loading-progress .progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

#loadingText {
    color: #4ecdc4;
    font-size: 0.9rem;
    text-align: center;
}

/* نظام الحماية */
.security-warning {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    animation: securityFadeIn 0.3s ease;
}

.warning-content {
    background: linear-gradient(135deg, #ff6b6b, #dc3545);
    color: white;
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 2px solid #fff;
}

.warning-content i {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: securityPulse 1s infinite;
}

.warning-content h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.warning-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.warning-content button {
    background: white;
    color: #dc3545;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.warning-content button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

@keyframes securityFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes securityPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات شريط التنقل */
.brand-badge {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-right: 0.5rem;
}

.admin-navbar {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-bottom: 2px solid #4ecdc4;
}

.admin-badge {
    background: linear-gradient(45deg, #ff6b6b, #f093fb);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #4ecdc4;
}

/* تحسينات القسم الرئيسي */
.hero-badge {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.title-decoration {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.title-decoration i {
    color: #4ecdc4;
    animation: twinkle 2s ease-in-out infinite;
}

.title-decoration i:nth-child(2) {
    animation-delay: 0.5s;
}

.title-decoration i:nth-child(3) {
    animation-delay: 1s;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.feature-badge {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    font-weight: 600;
    transition: all 0.3s ease;
}

.feature-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    font-size: 1.5rem;
    color: #4ecdc4;
    margin-bottom: 0.5rem;
}

.trust-indicators {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.trust-item i {
    color: #4ecdc4;
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.btn:hover .btn-shine {
    left: 100%;
}

/* تحسينات المميزات */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
    margin-top: 1rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.feature-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: linear-gradient(45deg, #ff6b6b, #f093fb);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.feature-card.premium {
    border: 2px solid #4ecdc4;
    position: relative;
}

.feature-stats {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-stats span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #4ecdc4;
}

/* جدول المقارنة */
.comparison-section {
    margin-top: 4rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    backdrop-filter: blur(15px);
}

.comparison-table {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    margin-top: 2rem;
}

.comparison-header {
    display: contents;
}

.comparison-item {
    padding: 1rem;
    text-align: center;
    border-radius: 10px;
    font-weight: 600;
}

.comparison-header .comparison-item {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.comparison-item.rix {
    background: rgba(78, 205, 196, 0.1);
    border: 1px solid #4ecdc4;
    color: #4ecdc4;
}

.comparison-item.others {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid #ff6b6b;
    color: #ff6b6b;
}

.comparison-row {
    display: contents;
}

/* لوحة تحكم الأدمن */
.admin-dashboard {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.admin-header {
    text-align: center;
    margin-bottom: 3rem;
}

.admin-quick-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.quick-stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 150px;
}

.quick-stat i {
    font-size: 2rem;
    color: #4ecdc4;
    margin-bottom: 0.5rem;
}

.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.admin-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.admin-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    color: #fff;
    margin: 0;
}

.card-content {
    padding: 1.5rem;
}

.card-tools {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.tool-label {
    color: #4ecdc4;
    font-size: 0.9rem;
}

/* نماذج الأدمن */
.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-control {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 0.9rem;
}

.form-control:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

.search-bar {
    position: relative;
    margin-bottom: 1rem;
}

.search-bar input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
}

.search-bar i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #4ecdc4;
}

/* قوائم البيانات */
.users-list, .tasks-list, .codes-list, .withdrawals-list {
    max-height: 400px;
    overflow-y: auto;
}

.user-item, .task-item, .code-item, .withdrawal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.user-item:hover, .task-item:hover, .code-item:hover, .withdrawal-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.user-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 1rem;
}

.online-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: #28a745;
    border: 2px solid #fff;
    border-radius: 50%;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.25rem;
}

.user-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    opacity: 0.8;
}

.user-actions, .task-actions, .code-actions, .withdrawal-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* عرض الكود المُنشأ */
.generated-code-display {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(78, 205, 196, 0.1);
    border: 2px solid #4ecdc4;
    border-radius: 15px;
    text-align: center;
}

.success-code h4 {
    color: #4ecdc4;
    margin-bottom: 1rem;
}

.code-display {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: #4ecdc4;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    letter-spacing: 2px;
}

/* حالات طلبات السحب */
.withdrawal-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* سجل الأنشطة */
.activity-log {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.activity-content {
    flex: 1;
}

.activity-message {
    color: #fff;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* إحصائيات متقدمة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-content h4 {
    color: #fff;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.stat-content .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #4ecdc4;
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

/* فلاتر */
.withdrawals-filter, .filter-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-color: #4ecdc4;
    color: white;
}

/* إعدادات المهام */
.task-settings {
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #fff;
    cursor: pointer;
    transition: color 0.3s ease;
}

.checkbox-label:hover {
    color: #4ecdc4;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #4ecdc4;
}

.checkbox-label span {
    font-size: 0.9rem;
}

/* رسائل فارغة */
.no-data {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    padding: 2rem;
    font-style: italic;
}

/* صفحة المهام */
.tasks-page {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.page-hero {
    text-align: center;
    margin-bottom: 4rem;
    padding: 3rem 0;
}

.page-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.quick-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
    flex-wrap: wrap;
}

.stat-box {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 200px;
    transition: transform 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-info {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    color: #fff;
}

/* المهام اليومية */
.daily-section {
    margin-bottom: 4rem;
}

.daily-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #fff;
}

.daily-tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.daily-task-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid transparent;
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.daily-task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.daily-task-card:hover {
    transform: translateY(-10px);
    border-color: #4ecdc4;
    box-shadow: 0 20px 40px rgba(78, 205, 196, 0.3);
}

.task-icon.daily {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
}

.task-content h3 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.task-content p {
    opacity: 0.8;
    margin-bottom: 1rem;
}

.task-reward {
    background: linear-gradient(45deg, #ff6b6b, #f093fb);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* فلتر المهام */
.tasks-filter {
    margin-bottom: 3rem;
}

.tasks-filter h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #fff;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.filter-btn:hover,
.filter-btn.active {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-color: #4ecdc4;
    transform: translateY(-2px);
}

/* شبكة المهام */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.task-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.task-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.task-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.task-difficulty {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.task-difficulty.easy {
    background: #28a745;
    color: white;
}

.task-difficulty.medium {
    background: #ffc107;
    color: #000;
}

.task-difficulty.hard {
    background: #dc3545;
    color: white;
}

.task-content {
    padding: 1.5rem;
}

.task-content h3 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.task-content p {
    opacity: 0.8;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-time,
.task-reward {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.task-time {
    color: #4ecdc4;
}

.task-reward {
    color: #ff6b6b;
    font-weight: 600;
}

.task-actions {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* تحدي الأسبوع */
.weekly-challenge {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid #4ecdc4;
    border-radius: 25px;
    padding: 3rem;
    margin-bottom: 4rem;
    text-align: center;
}

.challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.challenge-header h2 {
    color: #fff;
    margin: 0;
}

.challenge-timer {
    background: rgba(255, 107, 107, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    color: #ff6b6b;
    font-weight: 600;
}

.challenge-progress {
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    transition: width 0.3s ease;
}

.progress-text {
    color: #4ecdc4;
    font-weight: 600;
}

.challenge-rewards {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.reward-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 15px;
    color: #fff;
    font-weight: 600;
}

.reward-item i {
    color: #4ecdc4;
}

/* نصائح الخبراء */
.expert-tips {
    margin-bottom: 4rem;
}

.expert-tips h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.tip-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
}

.tip-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ff6b6b, #f093fb);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1rem;
}

.tip-card h3 {
    color: #fff;
    margin-bottom: 1rem;
}

.tip-card p {
    opacity: 0.8;
    line-height: 1.6;
}

/* الإنجازات */
.achievements-section {
    margin-bottom: 4rem;
}

.achievements-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.achievement-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.achievement-card.unlocked {
    border-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.1);
}

.achievement-card.locked {
    opacity: 0.6;
}

.achievement-card:hover {
    transform: translateY(-5px);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1rem;
}

.achievement-card.locked .achievement-icon {
    background: rgba(255, 255, 255, 0.2);
}

.achievement-card h3 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.achievement-card p {
    opacity: 0.8;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.achievement-reward {
    background: linear-gradient(45deg, #ff6b6b, #f093fb);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* نوافذ منبثقة للمهام */
.quiz-modal,
.ad-modal {
    max-width: 500px;
}

.quiz-question h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
}

.quiz-options {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.quiz-option {
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quiz-option:hover {
    border-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.1);
}

.ad-content {
    text-align: center;
}

.ad-video {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 3rem;
    margin-bottom: 2rem;
}

.ad-video i {
    font-size: 3rem;
    color: #4ecdc4;
    margin-bottom: 1rem;
}

.ad-timer {
    font-size: 1.5rem;
    color: #ff6b6b;
    font-weight: 600;
    margin-bottom: 2rem;
}

/* نافذة تتبع الوقت */
.time-tracking-modal {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(15px);
    border: 2px solid #4ecdc4;
    border-radius: 20px;
    padding: 1.5rem;
    min-width: 300px;
    z-index: 1000;
    animation: slideInUp 0.3s ease;
}

.tracking-content {
    text-align: center;
    color: #fff;
}

.tracking-content h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.time-display {
    margin-bottom: 1rem;
}

.time-spent {
    font-size: 2rem;
    font-weight: 700;
    color: #4ecdc4;
    margin-bottom: 0.5rem;
}

.time-required {
    font-size: 0.9rem;
    opacity: 0.8;
}

.time-tracking-modal .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.time-tracking-modal .progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.tracking-content p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسينات الاستجابة للجوال */
.nav-links.mobile-active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* صفحة السحب */
.withdraw-page {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.balance-display {
    margin-top: 3rem;
}

.balance-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid #4ecdc4;
    border-radius: 25px;
    padding: 3rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.balance-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.balance-info {
    flex: 1;
    text-align: center;
}

.balance-info h2 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.balance-info p {
    opacity: 0.8;
}

/* خيارات السحب */
.withdraw-options {
    margin: 4rem 0;
}

.withdraw-options h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.withdraw-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.withdraw-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.withdraw-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.withdraw-card.robux {
    border-color: #4ecdc4;
}

.withdraw-card.gamepass {
    border-color: #ff6b6b;
}

.withdraw-card.giftcard {
    border-color: #f093fb;
}

.withdraw-header {
    padding: 2rem;
    text-align: center;
    position: relative;
}

.withdraw-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
}

.withdraw-card.robux .withdraw-icon {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.withdraw-card.gamepass .withdraw-icon {
    background: linear-gradient(45deg, #ff6b6b, #f5576c);
}

.withdraw-card.giftcard .withdraw-icon {
    background: linear-gradient(45deg, #f093fb, #f5576c);
}

.withdraw-header h3 {
    color: #fff;
    margin-bottom: 1rem;
}

.withdraw-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.withdraw-badge.popular {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    color: white;
}

.withdraw-badge.exclusive {
    background: linear-gradient(45deg, #ff6b6b, #f5576c);
    color: white;
}

.withdraw-badge.new {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    color: white;
}

.withdraw-content {
    padding: 0 2rem 2rem;
}

.withdraw-features {
    margin-bottom: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #fff;
}

.feature i {
    color: #4ecdc4;
}

.withdraw-limits {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
}

.limit-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.limit-label {
    opacity: 0.8;
}

.limit-value {
    font-weight: 600;
    color: #4ecdc4;
}

.withdraw-actions {
    padding: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* إحصائيات السحب */
.withdraw-stats {
    margin: 4rem 0;
}

.withdraw-stats h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

/* سجل السحب */
.withdraw-history {
    margin: 4rem 0;
}

.withdraw-history h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.history-table {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    overflow: hidden;
}

.history-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    color: #4ecdc4;
}

.history-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.history-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

.history-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.pending {
    background: #ffc107;
    color: #000;
}

.status-badge.processing {
    background: #17a2b8;
    color: white;
}

.status-badge.completed {
    background: #28a745;
    color: white;
}

.status-badge.rejected {
    background: #dc3545;
    color: white;
}

.no-history {
    text-align: center;
    padding: 4rem;
    color: rgba(255, 255, 255, 0.6);
}

.no-history i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #4ecdc4;
}

/* نصائح السحب */
.withdraw-tips {
    margin: 4rem 0;
}

.withdraw-tips h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

/* تفاصيل السحب */
.withdraw-details {
    margin-bottom: 2rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-label {
    opacity: 0.8;
}

.detail-value {
    font-weight: 600;
    color: #4ecdc4;
}

.withdraw-timeline {
    margin-top: 2rem;
}

.withdraw-timeline h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.timeline {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    z-index: 2;
    background: #1a1a2e;
    padding: 0 1rem;
}

.timeline-item i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.timeline-item.completed i {
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.timeline-item span {
    font-size: 0.8rem;
    text-align: center;
    opacity: 0.8;
}

.timeline-item.completed span {
    color: #4ecdc4;
    opacity: 1;
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-features,
    .trust-indicators,
    .admin-quick-stats,
    .quick-stats {
        flex-direction: column;
        align-items: center;
    }

    .comparison-table {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .admin-grid,
    .withdraw-grid,
    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
    }

    .user-item,
    .task-item,
    .code-item,
    .withdrawal-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .user-actions,
    .task-actions,
    .code-actions,
    .withdrawal-actions {
        width: 100%;
        justify-content: center;
    }

    .balance-card {
        flex-direction: column;
        text-align: center;
    }

    .challenge-header {
        flex-direction: column;
        text-align: center;
    }

    .history-header,
    .history-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .timeline {
        flex-direction: column;
        gap: 1rem;
    }

    .timeline::before {
        display: none;
    }
}

/* صفحة الدعوات */
.invite-page {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

/* رابط الدعوة */
.invite-link-section {
    margin: 4rem 0;
}

.invite-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid #4ecdc4;
    border-radius: 25px;
    padding: 3rem;
    text-align: center;
}

.invite-card .card-header h2 {
    color: #fff;
    margin-bottom: 1rem;
}

.invite-card .card-header p {
    opacity: 0.8;
    margin-bottom: 2rem;
}

.invite-link-display {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.invite-link-display input {
    flex: 1;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1rem;
    text-align: center;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-social {
    padding: 0.75rem 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-social:hover {
    transform: translateY(-3px);
}

.btn-social.whatsapp {
    background: linear-gradient(45deg, #25d366, #128c7e);
}

.btn-social.telegram {
    background: linear-gradient(45deg, #0088cc, #005580);
}

.btn-social.facebook {
    background: linear-gradient(45deg, #1877f2, #0d47a1);
}

.btn-social.twitter {
    background: linear-gradient(45deg, #1da1f2, #0d8bd9);
}

/* مستويات الدعوات */
.invite-levels {
    margin: 4rem 0;
}

.invite-levels h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.level-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.level-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.level-card.bronze::before {
    background: linear-gradient(45deg, #cd7f32, #b8860b);
}

.level-card.silver::before {
    background: linear-gradient(45deg, #c0c0c0, #a8a8a8);
}

.level-card.gold::before {
    background: linear-gradient(45deg, #ffd700, #ffb347);
}

.level-card.diamond::before {
    background: linear-gradient(45deg, #b9f2ff, #00bfff);
}

.level-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.level-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
}

.level-card.bronze .level-icon {
    background: linear-gradient(45deg, #cd7f32, #b8860b);
}

.level-card.silver .level-icon {
    background: linear-gradient(45deg, #c0c0c0, #a8a8a8);
}

.level-card.gold .level-icon {
    background: linear-gradient(45deg, #ffd700, #ffb347);
}

.level-card.diamond .level-icon {
    background: linear-gradient(45deg, #b9f2ff, #00bfff);
}

.level-card h3 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.level-requirement {
    color: #4ecdc4;
    font-weight: 600;
    margin-bottom: 1rem;
}

.level-benefits {
    margin-bottom: 1.5rem;
}

.benefit {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.level-progress {
    margin-top: 1rem;
}

.level-progress .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.level-progress .progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.level-card.bronze .progress-fill {
    background: linear-gradient(45deg, #cd7f32, #b8860b);
}

.level-card.silver .progress-fill {
    background: linear-gradient(45deg, #c0c0c0, #a8a8a8);
}

.level-card.gold .progress-fill {
    background: linear-gradient(45deg, #ffd700, #ffb347);
}

.level-card.diamond .progress-fill {
    background: linear-gradient(45deg, #b9f2ff, #00bfff);
}

/* قائمة المدعوين */
.invited-users {
    margin: 4rem 0;
}

.invited-users h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.users-table {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    color: #4ecdc4;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.table-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

.table-item {
    display: flex;
    align-items: center;
    color: #fff;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge.active {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.status-badge.inactive {
    background: #6c757d;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.no-invites {
    text-align: center;
    padding: 4rem;
    color: rgba(255, 255, 255, 0.6);
}

.no-invites i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #4ecdc4;
}

/* مسابقة الدعوات */
.monthly-contest {
    margin: 4rem 0;
}

.monthly-contest h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #fff;
}

.contest-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 2px solid #ff6b6b;
    border-radius: 25px;
    overflow: hidden;
}

.contest-header {
    background: linear-gradient(45deg, #ff6b6b, #f093fb);
    padding: 2rem;
    text-align: center;
    color: white;
}

.contest-header h3 {
    margin-bottom: 1rem;
}

.contest-timer {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.contest-content {
    padding: 2rem;
}

.contest-prizes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.prize {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
}

.prize-rank {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.prize-details h4 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.contest-leaderboard h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.leaderboard-list {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    overflow: hidden;
}

.leader-item {
    display: grid;
    grid-template-columns: 50px 1fr auto;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    align-items: center;
}

.leader-item:last-child {
    border-bottom: none;
}

.leader-item.your-rank {
    background: rgba(78, 205, 196, 0.1);
    border: 1px solid #4ecdc4;
}

.leader-item .rank {
    font-weight: 700;
    color: #4ecdc4;
    text-align: center;
}

.leader-item .name {
    color: #fff;
}

.leader-item .invites {
    color: #4ecdc4;
    font-weight: 600;
}
