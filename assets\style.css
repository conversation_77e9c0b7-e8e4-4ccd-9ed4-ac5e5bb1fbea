/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #0a0a0a;
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* شريط التنقل */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    color: #667eea;
    font-size: 2rem;
    font-weight: 700;
}

.nav-brand a {
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-completed {
    background: #28a745;
    color: white;
    opacity: 0.7;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-full {
    width: 100%;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

/* الصفحة الرئيسية */
.hero {
    padding: 4rem 0;
    text-align: center;
    color: white;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.user-welcome {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    margin-top: 2rem;
}

.robux-count {
    font-weight: 700;
    color: #ffd700;
    font-size: 1.2em;
}

/* الميزات */
.features {
    padding: 4rem 0;
    background: white;
}

.features h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* كيف يعمل */
.how-it-works {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.how-it-works h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.step {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: white;
    color: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.stat-card.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* النماذج */
.auth-section {
    padding: 4rem 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.auth-card {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    margin: 0 auto;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group small {
    color: #6c757d;
    font-size: 0.875rem;
}

/* التنبيهات */
.alert {
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* التذييل */
footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: auto;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-links {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .features-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .steps {
        grid-template-columns: 1fr;
    }
}

/* صفحة لوحة التحكم */
.dashboard {
    padding: 2rem 0;
    background: white;
    min-height: 80vh;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.user-balance {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    display: inline-block;
    margin-top: 1rem;
    font-weight: 600;
}

.quick-actions {
    margin: 3rem 0;
}

.quick-actions h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.action-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.action-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.dashboard-content {
    margin: 3rem 0;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.content-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.content-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.tasks-list,
.withdrawals-list {
    max-height: 300px;
    overflow-y: auto;
}

.task-item,
.withdrawal-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.task-item:hover,
.withdrawal-item:hover {
    background-color: #f8f9fa;
}

.task-info,
.withdrawal-info {
    flex: 1;
}

.task-info h4,
.withdrawal-info h4 {
    margin-bottom: 0.25rem;
    color: #333;
}

.task-date,
.withdrawal-date {
    font-size: 0.875rem;
    color: #6c757d;
}

.task-reward {
    font-weight: 700;
    color: #28a745;
}

.withdrawal-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-paid {
    background: #d4edda;
    color: #155724;
}

.account-info {
    margin: 3rem 0;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.account-info h3 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.info-label {
    font-weight: 600;
    color: #333;
}

.info-value {
    color: #667eea;
    font-weight: 500;
}

/* صفحة المهام */
.earn-section {
    padding: 2rem 0;
    background: white;
    min-height: 80vh;
}

.earn-header {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.earn-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.task-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.task-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.task-card.completed {
    opacity: 0.7;
    border-color: #28a745;
    background: #f8fff9;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.task-header h3 {
    flex: 1;
    margin-left: 1rem;
    color: #333;
}

.task-reward {
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    min-width: 80px;
}

.reward-amount {
    font-size: 1.2rem;
    font-weight: 700;
    display: block;
}

.reward-label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.task-description {
    margin-bottom: 1.5rem;
    color: #6c757d;
    line-height: 1.6;
}

.task-footer {
    text-align: center;
}

.earn-tips {
    margin: 4rem 0;
}

.earn-tips h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.tip-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-3px);
}

.tip-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.tip-card h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.tip-card p {
    color: #6c757d;
    font-size: 0.9rem;
}

.progress-summary {
    margin: 3rem 0;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.progress-summary h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.progress-stat {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.progress-stat .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    display: block;
}

.progress-stat .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

/* صفحة السحب */
.withdraw-section {
    padding: 2rem 0;
    background: white;
    min-height: 80vh;
}

.withdraw-header {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.withdraw-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.pending-request {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    border: 2px solid #ffc107;
}

.pending-request h3 {
    color: #856404;
    margin-bottom: 1rem;
}

.request-details {
    margin-bottom: 1rem;
}

.request-details p {
    margin-bottom: 0.5rem;
    color: #856404;
}

.wait-message {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
}

.withdraw-form-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.withdraw-form-section h3 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.insufficient-balance {
    text-align: center;
    padding: 2rem;
    background: #f8d7da;
    border-radius: 15px;
    color: #721c24;
}

.insufficient-balance p {
    margin-bottom: 1rem;
}

.withdraw-form {
    margin-bottom: 2rem;
}

.withdraw-info {
    background: #e7f3ff;
    padding: 1.5rem;
    border-radius: 10px;
    margin: 1.5rem 0;
    border-left: 4px solid #007bff;
}

.withdraw-info h4 {
    color: #004085;
    margin-bottom: 1rem;
}

.withdraw-info ul {
    list-style: none;
    padding: 0;
}

.withdraw-info li {
    padding: 0.5rem 0;
    color: #004085;
    position: relative;
    padding-right: 1.5rem;
}

.withdraw-info li:before {
    content: "✓";
    position: absolute;
    right: 0;
    color: #28a745;
    font-weight: bold;
}

.withdraw-history {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.withdraw-history h3 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.history-table {
    overflow-x: auto;
}

.history-table table {
    width: 100%;
    border-collapse: collapse;
}

.history-table th,
.history-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.history-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

/* لوحة الأدمن */
.admin-section {
    padding: 2rem 0;
    background: white;
    min-height: 80vh;
}

.admin-header {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.admin-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.admin-stats {
    margin-bottom: 3rem;
}

.admin-stats h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.admin-section-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.admin-section-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.table-responsive {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.admin-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

.text-success {
    color: #28a745;
    font-weight: 600;
}

.add-task-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.add-task-form h4 {
    margin-bottom: 1.5rem;
    color: #333;
}

.admin-form {
    margin-bottom: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.tasks-list h4 {
    margin-bottom: 1rem;
    color: #333;
}

.status-active {
    background: #d4edda;
    color: #155724;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
}

/* النافذة المنبثقة */
.modal {
    position: fixed;
    z-index: 1000;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

.modal-content h3 {
    margin-bottom: 1.5rem;
    color: #333;
    text-align: center;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

/* تحسينات إضافية */
.quick-stats {
    padding: 4rem 0;
    background: #f8f9fa;
}

.quick-stats h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.features-preview {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #e7f3ff;
    border-radius: 10px;
}

.features-preview h3 {
    color: #004085;
    margin-bottom: 1rem;
}

.features-preview ul {
    list-style: none;
    padding: 0;
}

.features-preview li {
    padding: 0.5rem 0;
    color: #004085;
}

.login-benefits {
    margin-top: 2rem;
    text-align: center;
}

.login-benefits h3 {
    color: #333;
    margin-bottom: 1.5rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.benefit-icon {
    font-size: 1.2rem;
}

/* استجابة للشاشات الصغيرة - تحديثات إضافية */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .task-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .task-header h3 {
        margin: 0 0 1rem 0;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
