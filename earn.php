<?php
session_start();
require_once 'includes/db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

$user_data = get_user_data($_SESSION['user_id']);
$user_id = $_SESSION['user_id'];
$message = '';

// معالجة إكمال المهمة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['complete_task'])) {
    $task_id = (int)$_POST['task_id'];
    
    // التحقق من وجود المهمة
    $stmt = $pdo->prepare("SELECT * FROM tasks WHERE id = ? AND is_active = 1");
    $stmt->execute([$task_id]);
    $task = $stmt->fetch();
    
    if ($task) {
        // التحقق من عدم إكمال المهمة مسبقاً
        $stmt = $pdo->prepare("SELECT id FROM task_logs WHERE user_id = ? AND task_id = ?");
        $stmt->execute([$user_id, $task_id]);
        
        if (!$stmt->fetch()) {
            // إضافة المهمة للسجل وتحديث الرصيد
            $pdo->beginTransaction();
            try {
                $stmt = $pdo->prepare("INSERT INTO task_logs (user_id, task_id) VALUES (?, ?)");
                $stmt->execute([$user_id, $task_id]);
                
                update_robux_balance($user_id, $task['reward']);
                
                $pdo->commit();
                $message = "تم إكمال المهمة بنجاح! حصلت على {$task['reward']} روبكس 🎉";
            } catch (Exception $e) {
                $pdo->rollback();
                $message = "حدث خطأ أثناء إكمال المهمة";
            }
        } else {
            $message = "لقد أكملت هذه المهمة من قبل";
        }
    } else {
        $message = "المهمة غير موجودة أو غير متاحة";
    }
}

// جلب المهام المتاحة
$stmt = $pdo->prepare("SELECT * FROM tasks WHERE is_active = 1 ORDER BY reward DESC");
$stmt->execute();
$available_tasks = $stmt->fetchAll();

// جلب المهام المكتملة للمستخدم
$stmt = $pdo->prepare("SELECT task_id FROM task_logs WHERE user_id = ?");
$stmt->execute([$user_id]);
$completed_task_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المهام - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="dashboard.php">لوحة التحكم</a>
                    <a href="earn.php" class="active">المهام</a>
                    <a href="withdraw.php">سحب روبكس</a>
                    <?php if (is_admin()): ?>
                        <a href="admin.php">الأدمن</a>
                    <?php endif; ?>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="earn-section">
            <div class="container">
                <div class="earn-header">
                    <h1>🎯 المهام المتاحة</h1>
                    <p>أكمل المهام واربح نقاط روبكس</p>
                    <div class="user-balance">
                        رصيدك الحالي: <span class="robux-count"><?php echo $user_data['robux_balance']; ?> روبكس</span>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        ✅ <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($available_tasks)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">📭</div>
                        <h3>لا توجد مهام متاحة حالياً</h3>
                        <p>تحقق مرة أخرى لاحقاً للحصول على مهام جديدة</p>
                        <a href="dashboard.php" class="btn btn-primary">العودة للوحة التحكم</a>
                    </div>
                <?php else: ?>
                    <div class="tasks-grid">
                        <?php foreach ($available_tasks as $task): ?>
                            <?php $is_completed = in_array($task['id'], $completed_task_ids); ?>
                            <div class="task-card <?php echo $is_completed ? 'completed' : ''; ?>">
                                <div class="task-header">
                                    <h3><?php echo htmlspecialchars($task['title']); ?></h3>
                                    <div class="task-reward">
                                        <span class="reward-amount"><?php echo $task['reward']; ?></span>
                                        <span class="reward-label">روبكس</span>
                                    </div>
                                </div>
                                
                                <div class="task-description">
                                    <p><?php echo nl2br(htmlspecialchars($task['description'])); ?></p>
                                </div>
                                
                                <div class="task-footer">
                                    <?php if ($is_completed): ?>
                                        <button class="btn btn-completed" disabled>
                                            ✅ تم الإكمال
                                        </button>
                                    <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                            <button type="submit" name="complete_task" class="btn btn-primary" 
                                                    onclick="return confirm('هل أنت متأكد من إكمال هذه المهمة؟')">
                                                🚀 أكمل المهمة
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <div class="earn-tips">
                    <h3>💡 نصائح لربح المزيد</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <div class="tip-icon">⏰</div>
                            <h4>تحقق يومياً</h4>
                            <p>مهام جديدة تضاف بانتظام</p>
                        </div>
                        <div class="tip-card">
                            <div class="tip-icon">🎯</div>
                            <h4>أكمل جميع المهام</h4>
                            <p>لا تفوت أي فرصة لربح النقاط</p>
                        </div>
                        <div class="tip-card">
                            <div class="tip-icon">💎</div>
                            <h4>اجمع النقاط</h4>
                            <p>كلما جمعت أكثر، كلما سحبت أكثر</p>
                        </div>
                        <div class="tip-card">
                            <div class="tip-icon">⚡</div>
                            <h4>اسحب بانتظام</h4>
                            <p>لا تنس سحب نقاطك عند الوصول للحد الأدنى</p>
                        </div>
                    </div>
                </div>

                <div class="progress-summary">
                    <h3>📊 ملخص التقدم</h3>
                    <div class="progress-stats">
                        <div class="progress-stat">
                            <span class="stat-number"><?php echo count($completed_task_ids); ?></span>
                            <span class="stat-label">مهام مكتملة</span>
                        </div>
                        <div class="progress-stat">
                            <span class="stat-number"><?php echo count($available_tasks) - count($completed_task_ids); ?></span>
                            <span class="stat-label">مهام متبقية</span>
                        </div>
                        <div class="progress-stat">
                            <span class="stat-number"><?php echo $user_data['robux_balance']; ?></span>
                            <span class="stat-label">روبكس مكتسب</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
</body>
</html>
