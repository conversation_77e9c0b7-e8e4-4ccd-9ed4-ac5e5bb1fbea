{"withdrawals": [{"id": 1001, "userId": 2, "username": "testuser", "type": "robux", "amount": 250, "robloxUsername": "TestUser_Roblox", "status": "completed", "requestDate": "2024-12-20T14:20:00.000Z", "processedDate": "2024-12-20T18:45:00.000Z", "processedBy": "admin", "notes": "تم التحويل بنجاح", "transactionId": "TXN_001_250R"}, {"id": 1002, "userId": 3, "username": "progamer123", "type": "robux", "amount": 500, "robloxUsername": "ProGamer_Official", "status": "completed", "requestDate": "2024-12-15T16:45:00.000Z", "processedDate": "2024-12-16T09:30:00.000Z", "processedBy": "admin", "notes": "تم التحويل بنجاح - مستخدم VIP", "transactionId": "TXN_002_500R"}, {"id": 1003, "userId": 3, "username": "progamer123", "type": "gamepass", "amount": 500, "robloxUsername": "ProGamer_Official", "status": "processing", "requestDate": "2024-12-28T10:20:00.000Z", "processedDate": null, "processedBy": null, "notes": "في انتظار المعالجة - تم التحقق من عضوية الجروب", "transactionId": null, "gamepassValue": 800, "groupMembershipConfirmed": true, "membershipDuration": 14}, {"id": 1004, "userId": 4, "username": "robuxhunter", "type": "robux", "amount": 300, "robloxUsername": "RobuxHunter2024", "status": "completed", "requestDate": "2024-12-22T12:30:00.000Z", "processedDate": "2024-12-22T20:15:00.000Z", "processedBy": "admin", "notes": "تم التحويل بنجاح", "transactionId": "TXN_003_300R"}, {"id": 1005, "userId": 2, "username": "testuser", "type": "robux", "amount": 150, "robloxUsername": "TestUser_Roblox", "status": "pending", "requestDate": "2024-12-29T09:45:00.000Z", "processedDate": null, "processedBy": null, "notes": "طلب جديد في انتظار المراجعة", "transactionId": null}, {"id": 1006, "userId": 5, "username": "gamemaster", "type": "gamepass", "amount": 500, "robloxUsername": "GameMaster_Pro", "status": "rejected", "requestDate": "2024-12-25T14:20:00.000Z", "processedDate": "2024-12-26T10:00:00.000Z", "processedBy": "admin", "notes": "مرفوض - ل<PERSON> يك<PERSON>ل المتطلبات (أقل من 5 مهام)", "transactionId": null, "rejectionReason": "insufficient_tasks"}, {"id": 1007, "userId": 4, "username": "robuxhunter", "type": "robux", "amount": 200, "robloxUsername": "RobuxHunter2024", "status": "processing", "requestDate": "2024-12-29T11:20:00.000Z", "processedDate": null, "processedBy": null, "notes": "قيد المعالجة - تم التحقق من البيانات", "transactionId": null}], "stats": {"totalWithdrawals": 7, "completedWithdrawals": 3, "pendingWithdrawals": 1, "processingWithdrawals": 2, "rejectedWithdrawals": 1, "totalRobuxWithdrawn": 1050, "totalGamepassRequests": 2, "averageProcessingTime": "16.5 hours", "successRate": "85.7%", "lastUpdated": "2024-12-29T12:00:00.000Z"}, "settings": {"minRobuxWithdrawal": 100, "maxRobuxWithdrawal": 10000, "minGamepassRequirement": 500, "gamepassGroupId": "12345678", "gamepassMembershipDays": 14, "gamepassMinTasks": 5, "processingTimeHours": 24, "autoApprovalLimit": 1000, "requireAdminApproval": true}}