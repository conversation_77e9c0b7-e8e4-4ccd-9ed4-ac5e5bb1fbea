<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المهام - Rix | اربح روبكس مجاناً</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-gamepad"></i> Rix</h1>
            </div>
            <div class="nav-links" id="navLinks">
                <a href="index.html" class="nav-link">الرئيسية</a>
                <a href="tasks.html" class="nav-link active">المهام</a>
                <a href="withdraw.html" class="nav-link">السحب</a>
                <a href="codes.html" class="nav-link">الأكواد</a>
                <a href="invite.html" class="nav-link">الدعوات</a>
                <a href="dashboard.html" class="nav-link">لوحة التحكم</a>
                <div class="user-balance" id="userBalance">0 روبكس</div>
                <button class="btn btn-secondary" onclick="logout()">تسجيل الخروج</button>
            </div>
        </div>
    </nav>

    <!-- قسم المهام -->
    <section class="tasks-page">
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-tasks"></i> المهام المتاحة</h1>
                <p>أكمل المهام واربح نقاط الروبكس</p>
                <div class="stats-bar">
                    <div class="stat-item">
                        <span class="stat-number" id="completedCount">0</span>
                        <span class="stat-label">مهام مكتملة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="availableCount">8</span>
                        <span class="stat-label">مهام متاحة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="totalEarned">0</span>
                        <span class="stat-label">روبكس مكتسب</span>
                    </div>
                </div>
            </div>

            <!-- فلتر المهام -->
            <div class="tasks-filter">
                <h3><i class="fas fa-filter"></i> تصنيف المهام</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">جميع المهام</button>
                    <button class="filter-btn" data-filter="social">وسائل التواصل</button>
                    <button class="filter-btn" data-filter="video">مشاهدة فيديو</button>
                    <button class="filter-btn" data-filter="app">تطبيقات</button>
                    <button class="filter-btn" data-filter="group">جروبات</button>
                </div>
            </div>

            <!-- شبكة المهام -->
            <div class="tasks-grid" id="tasksGrid">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>

            <!-- مهام يومية خاصة -->
            <div class="daily-tasks">
                <h2><i class="fas fa-calendar-day"></i> المهام اليومية</h2>
                <div class="daily-grid">
                    <div class="daily-task">
                        <div class="daily-icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="daily-content">
                            <h3>تسجيل الدخول اليومي</h3>
                            <p>سجل دخولك يومياً للحصول على مكافأة</p>
                            <div class="daily-reward">+10 روبكس</div>
                        </div>
                        <button class="btn btn-success" onclick="claimDailyLogin()">
                            <i class="fas fa-gift"></i> استلم
                        </button>
                    </div>

                    <div class="daily-task">
                        <div class="daily-icon">
                            <i class="fas fa-share"></i>
                        </div>
                        <div class="daily-content">
                            <h3>مشاركة يومية</h3>
                            <p>شارك الموقع مع صديق واحد يومياً</p>
                            <div class="daily-reward">+15 روبكس</div>
                        </div>
                        <button class="btn btn-primary" onclick="shareDaily()">
                            <i class="fas fa-share-alt"></i> شارك
                        </button>
                    </div>

                    <div class="daily-task">
                        <div class="daily-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="daily-content">
                            <h3>مشاهدة إعلان</h3>
                            <p>شاهد 3 إعلانات قصيرة يومياً</p>
                            <div class="daily-reward">+20 روبكس</div>
                        </div>
                        <button class="btn btn-warning" onclick="watchAds()">
                            <i class="fas fa-play"></i> شاهد
                        </button>
                    </div>
                </div>
            </div>

            <!-- تحديات أسبوعية -->
            <div class="weekly-challenges">
                <h2><i class="fas fa-trophy"></i> التحديات الأسبوعية</h2>
                <div class="challenge-card">
                    <div class="challenge-header">
                        <h3>تحدي الأسبوع: ملك المهام</h3>
                        <div class="challenge-timer">
                            <i class="fas fa-clock"></i>
                            <span id="weeklyTimer">5 أيام متبقية</span>
                        </div>
                    </div>
                    <div class="challenge-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                        <span class="progress-text">6/10 مهام مكتملة</span>
                    </div>
                    <div class="challenge-reward">
                        <i class="fas fa-gift"></i>
                        <span>مكافأة: 200 روبكس + جيم باس خاص</span>
                    </div>
                </div>
            </div>

            <!-- نصائح وإرشادات -->
            <div class="tasks-tips">
                <h2><i class="fas fa-lightbulb"></i> نصائح لزيادة الأرباح</h2>
                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3>أكمل المهام يومياً</h3>
                        <p>المهام اليومية تعطي مكافآت إضافية ومضاعفة</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3>ادع أصدقائك</h3>
                        <p>احصل على 25 روبكس عن كل صديق + 10% من أرباحه</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <h3>حافظ على النشاط</h3>
                        <p>النشاط المستمر يفتح مهام خاصة بمكافآت أكبر</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3>اشترك في الجروب</h3>
                        <p>أعضاء الجروب يحصلون على مهام حصرية</p>
                    </div>
                </div>
            </div>

            <!-- إحصائيات شخصية -->
            <div class="personal-stats">
                <h2><i class="fas fa-chart-line"></i> إحصائياتك الشخصية</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="stat-content">
                            <h3>أيام النشاط</h3>
                            <div class="stat-number">7</div>
                            <p>يوم متتالي</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div class="stat-content">
                            <h3>المستوى</h3>
                            <div class="stat-number">3</div>
                            <p>مستخدم نشط</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3>معدل الإكمال</h3>
                            <div class="stat-number">85%</div>
                            <p>من المهام</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-content">
                            <h3>متوسط الربح</h3>
                            <div class="stat-number">45</div>
                            <p>روبكس/يوم</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-gamepad"></i> Rix</h3>
                    <p>موقع آمن ومضمون لربح الروبكس والجيم باس المجاني</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="tasks.html">المهام</a></li>
                        <li><a href="withdraw.html">السحب</a></li>
                        <li><a href="codes.html">الأكواد</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <script src="assets/script.js"></script>
    <script>
        // تحميل صفحة المهام
        document.addEventListener('DOMContentLoaded', function() {
            if (!rix.currentUser) {
                window.location.href = 'index.html';
                return;
            }
            
            loadTasksPage();
            updateTasksStats();
            setupTasksFilter();
        });

        function loadTasksPage() {
            rix.loadTasks();
            updateNavigation();
        }

        function updateTasksStats() {
            document.getElementById('completedCount').textContent = rix.completedTasks.length;
            document.getElementById('totalEarned').textContent = rix.userBalance;
            document.getElementById('userBalance').textContent = `${rix.userBalance} روبكس`;
        }

        function setupTasksFilter() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const filter = this.getAttribute('data-filter');
                    filterTasks(filter);
                });
            });
        }

        function filterTasks(filter) {
            const taskCards = document.querySelectorAll('.task-card');
            taskCards.forEach(card => {
                if (filter === 'all' || card.classList.contains(filter)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function claimDailyLogin() {
            rix.userBalance += 10;
            rix.saveUserData();
            rix.showSuccessMessage('تم استلام مكافأة تسجيل الدخول اليومي! +10 روبكس');
            updateTasksStats();
        }

        function shareDaily() {
            const shareText = 'انضم لموقع Rix واربح روبكس مجاناً!';
            const shareUrl = window.location.origin + '?ref=' + rix.currentUser.username;
            
            if (navigator.share) {
                navigator.share({
                    title: 'Rix - ربح روبكس مجاناً',
                    text: shareText,
                    url: shareUrl
                });
            } else {
                navigator.clipboard.writeText(shareUrl);
                rix.showSuccessMessage('تم نسخ رابط المشاركة! +15 روبكس');
            }
            
            rix.userBalance += 15;
            rix.saveUserData();
            updateTasksStats();
        }

        function watchAds() {
            // محاكاة مشاهدة إعلان
            rix.showSuccessMessage('جاري تحميل الإعلان...');
            
            setTimeout(() => {
                rix.userBalance += 20;
                rix.saveUserData();
                rix.showSuccessMessage('تم مشاهدة الإعلان بنجاح! +20 روبكس');
                updateTasksStats();
            }, 3000);
        }

        function updateNavigation() {
            document.getElementById('navLinks').innerHTML = `
                <a href="index.html" class="nav-link">الرئيسية</a>
                <a href="tasks.html" class="nav-link active">المهام</a>
                <a href="withdraw.html" class="nav-link">السحب</a>
                <a href="codes.html" class="nav-link">الأكواد</a>
                <a href="invite.html" class="nav-link">الدعوات</a>
                <a href="dashboard.html" class="nav-link">لوحة التحكم</a>
                <div class="user-balance">${rix.userBalance} روبكس</div>
                <button class="btn btn-secondary" onclick="logout()">تسجيل الخروج</button>
            `;
        }

        function logout() {
            rix.logout();
        }
    </script>
</body>
</html>
