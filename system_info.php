<?php
/**
 * معلومات النظام والتحقق من التثبيت
 * System Information and Installation Check
 */

session_start();
require_once 'includes/db.php';

// التحقق من صلاحيات الأدمن
if (!is_admin()) {
    die('Access denied - Admin only');
}

// فحص متطلبات النظام
function checkSystemRequirements() {
    $requirements = [
        'PHP Version' => [
            'required' => '7.4.0',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.4.0', '>=')
        ],
        'PDO Extension' => [
            'required' => 'Enabled',
            'current' => extension_loaded('pdo') ? 'Enabled' : 'Disabled',
            'status' => extension_loaded('pdo')
        ],
        'PDO MySQL' => [
            'required' => 'Enabled',
            'current' => extension_loaded('pdo_mysql') ? 'Enabled' : 'Disabled',
            'status' => extension_loaded('pdo_mysql')
        ],
        'Session Support' => [
            'required' => 'Enabled',
            'current' => function_exists('session_start') ? 'Enabled' : 'Disabled',
            'status' => function_exists('session_start')
        ],
        'JSON Extension' => [
            'required' => 'Enabled',
            'current' => extension_loaded('json') ? 'Enabled' : 'Disabled',
            'status' => extension_loaded('json')
        ],
        'mbstring Extension' => [
            'required' => 'Enabled',
            'current' => extension_loaded('mbstring') ? 'Enabled' : 'Disabled',
            'status' => extension_loaded('mbstring')
        ]
    ];
    
    return $requirements;
}

// فحص قاعدة البيانات
function checkDatabase() {
    global $pdo;
    
    $tables = ['users', 'tasks', 'task_logs', 'withdraw_requests', 'settings'];
    $database_status = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            $database_status[$table] = [
                'exists' => true,
                'count' => $count,
                'status' => true
            ];
        } catch (Exception $e) {
            $database_status[$table] = [
                'exists' => false,
                'count' => 0,
                'status' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    return $database_status;
}

// فحص الملفات المطلوبة
function checkRequiredFiles() {
    $required_files = [
        'index.php' => 'الصفحة الرئيسية',
        'login.php' => 'صفحة تسجيل الدخول',
        'register.php' => 'صفحة التسجيل',
        'dashboard.php' => 'لوحة المستخدم',
        'earn.php' => 'صفحة المهام',
        'withdraw.php' => 'صفحة السحب',
        'admin.php' => 'لوحة الأدمن',
        'includes/db.php' => 'ملف قاعدة البيانات',
        'assets/style.css' => 'ملف التصميم',
        'assets/script.js' => 'ملف JavaScript',
        'config.php' => 'ملف التكوين',
        '.htaccess' => 'ملف الحماية'
    ];
    
    $file_status = [];
    
    foreach ($required_files as $file => $description) {
        $file_status[$file] = [
            'description' => $description,
            'exists' => file_exists($file),
            'readable' => file_exists($file) && is_readable($file),
            'size' => file_exists($file) ? filesize($file) : 0
        ];
    }
    
    return $file_status;
}

// فحص الصلاحيات
function checkPermissions() {
    $directories = [
        '.' => 'المجلد الرئيسي',
        'assets' => 'مجلد الأصول',
        'includes' => 'مجلد التضمين'
    ];
    
    $permission_status = [];
    
    foreach ($directories as $dir => $description) {
        if (is_dir($dir)) {
            $permission_status[$dir] = [
                'description' => $description,
                'readable' => is_readable($dir),
                'writable' => is_writable($dir),
                'permissions' => substr(sprintf('%o', fileperms($dir)), -4)
            ];
        }
    }
    
    return $permission_status;
}

// جمع معلومات النظام
$system_requirements = checkSystemRequirements();
$database_status = checkDatabase();
$file_status = checkRequiredFiles();
$permission_status = checkPermissions();

// حساب النتيجة الإجمالية
$total_checks = 0;
$passed_checks = 0;

foreach ($system_requirements as $req) {
    $total_checks++;
    if ($req['status']) $passed_checks++;
}

foreach ($database_status as $table) {
    $total_checks++;
    if ($table['status']) $passed_checks++;
}

foreach ($file_status as $file) {
    $total_checks++;
    if ($file['exists'] && $file['readable']) $passed_checks++;
}

$health_percentage = round(($passed_checks / $total_checks) * 100);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات النظام - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="admin.php">لوحة الأدمن</a>
                    <a href="dashboard.php">لوحة التحكم</a>
                    <a href="index.php">الرئيسية</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="admin-section">
            <div class="container">
                <div class="admin-header">
                    <h1>🔧 معلومات النظام</h1>
                    <p>فحص حالة النظام والتحقق من التثبيت</p>
                </div>

                <!-- نتيجة الفحص الإجمالية -->
                <div class="admin-section-card">
                    <h3>📊 نتيجة الفحص الإجمالية</h3>
                    <div class="health-score">
                        <div class="health-circle">
                            <div class="health-percentage"><?php echo $health_percentage; ?>%</div>
                            <div class="health-label">صحة النظام</div>
                        </div>
                        <div class="health-details">
                            <p><strong>اجتاز:</strong> <?php echo $passed_checks; ?> من <?php echo $total_checks; ?> فحص</p>
                            <p><strong>الحالة:</strong> 
                                <span class="status-badge status-<?php echo $health_percentage >= 90 ? 'excellent' : ($health_percentage >= 70 ? 'good' : 'warning'); ?>">
                                    <?php 
                                    if ($health_percentage >= 90) echo 'ممتاز';
                                    elseif ($health_percentage >= 70) echo 'جيد';
                                    else echo 'يحتاج تحسين';
                                    ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- متطلبات النظام -->
                <div class="admin-section-card">
                    <h3>⚙️ متطلبات النظام</h3>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>المتطلب</th>
                                    <th>المطلوب</th>
                                    <th>الحالي</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($system_requirements as $name => $req): ?>
                                    <tr>
                                        <td><?php echo $name; ?></td>
                                        <td><?php echo $req['required']; ?></td>
                                        <td><?php echo $req['current']; ?></td>
                                        <td>
                                            <span class="status status-<?php echo $req['status'] ? 'passed' : 'failed'; ?>">
                                                <?php echo $req['status'] ? '✅ مُجتاز' : '❌ فاشل'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- حالة قاعدة البيانات -->
                <div class="admin-section-card">
                    <h3>🗄️ حالة قاعدة البيانات</h3>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الجدول</th>
                                    <th>الحالة</th>
                                    <th>عدد السجلات</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($database_status as $table => $status): ?>
                                    <tr>
                                        <td><?php echo $table; ?></td>
                                        <td>
                                            <span class="status status-<?php echo $status['status'] ? 'passed' : 'failed'; ?>">
                                                <?php echo $status['exists'] ? '✅ موجود' : '❌ مفقود'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $status['count']; ?></td>
                                        <td>
                                            <?php 
                                            if (!$status['status'] && isset($status['error'])) {
                                                echo '<small class="text-danger">' . htmlspecialchars($status['error']) . '</small>';
                                            } else {
                                                echo '<small class="text-success">يعمل بشكل طبيعي</small>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- حالة الملفات -->
                <div class="admin-section-card">
                    <h3>📁 حالة الملفات المطلوبة</h3>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الملف</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>الحجم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($file_status as $file => $status): ?>
                                    <tr>
                                        <td><code><?php echo $file; ?></code></td>
                                        <td><?php echo $status['description']; ?></td>
                                        <td>
                                            <span class="status status-<?php echo $status['exists'] && $status['readable'] ? 'passed' : 'failed'; ?>">
                                                <?php 
                                                if ($status['exists'] && $status['readable']) {
                                                    echo '✅ متاح';
                                                } elseif ($status['exists']) {
                                                    echo '⚠️ غير قابل للقراءة';
                                                } else {
                                                    echo '❌ مفقود';
                                                }
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            if ($status['size'] > 0) {
                                                echo number_format($status['size'] / 1024, 2) . ' KB';
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- معلومات الخادم -->
                <div class="admin-section-card">
                    <h3>🖥️ معلومات الخادم</h3>
                    <div class="server-info-grid">
                        <div class="info-item">
                            <strong>نظام التشغيل:</strong>
                            <span><?php echo PHP_OS; ?></span>
                        </div>
                        <div class="info-item">
                            <strong>إصدار PHP:</strong>
                            <span><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="info-item">
                            <strong>خادم الويب:</strong>
                            <span><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></span>
                        </div>
                        <div class="info-item">
                            <strong>الذاكرة المحددة:</strong>
                            <span><?php echo ini_get('memory_limit'); ?></span>
                        </div>
                        <div class="info-item">
                            <strong>الحد الأقصى لوقت التنفيذ:</strong>
                            <span><?php echo ini_get('max_execution_time'); ?> ثانية</span>
                        </div>
                        <div class="info-item">
                            <strong>المنطقة الزمنية:</strong>
                            <span><?php echo date_default_timezone_get(); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <style>
        .health-score {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
        }
        
        .health-circle {
            text-align: center;
            min-width: 120px;
        }
        
        .health-percentage {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .health-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .health-details {
            flex: 1;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-excellent {
            background: #d4edda;
            color: #155724;
        }
        
        .status-good {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-warning {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-passed {
            background: #d4edda;
            color: #155724;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .server-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .text-success {
            color: #28a745;
        }
        
        .text-danger {
            color: #dc3545;
        }
        
        code {
            background: #f8f9fa;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>

    <script src="assets/script.js"></script>
</body>
</html>
