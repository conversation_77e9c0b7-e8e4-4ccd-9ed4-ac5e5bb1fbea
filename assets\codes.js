// نظام الأكواد المتطور
class CodesSystem {
    constructor() {
        this.availableCodes = [];
        this.userCodesHistory = [];
        this.userStats = {
            totalCodes: 0,
            usedCodes: 0,
            earnedFromCodes: 0
        };
        this.init();
    }

    init() {
        this.checkLoginStatus();
        this.loadUserData();
        this.generateAvailableCodes();
        this.setupEventListeners();
        this.updateUI();
        this.startTimers();
    }

    checkLoginStatus() {
        if (!rix.currentUser) {
            window.location.href = 'index.html';
            return;
        }
    }

    loadUserData() {
        const savedHistory = localStorage.getItem('codesHistory_' + rix.currentUser.username);
        if (savedHistory) {
            this.userCodesHistory = JSON.parse(savedHistory);
        }

        const savedStats = localStorage.getItem('codesStats_' + rix.currentUser.username);
        if (savedStats) {
            this.userStats = JSON.parse(savedStats);
        }
    }

    saveUserData() {
        localStorage.setItem('codesHistory_' + rix.currentUser.username, JSON.stringify(this.userCodesHistory));
        localStorage.setItem('codesStats_' + rix.currentUser.username, JSON.stringify(this.userStats));
    }

    generateAvailableCodes() {
        this.availableCodes = [
            {
                code: 'RIX100',
                points: 100,
                description: 'كود ترحيبي للمستخدمين الجدد',
                type: 'welcome',
                icon: 'fas fa-gift',
                rarity: 'common',
                expiryDays: null
            },
            {
                code: 'WELCOME50',
                points: 50,
                description: 'مكافأة الانضمام للموقع',
                type: 'welcome',
                icon: 'fas fa-hand-wave',
                rarity: 'common',
                expiryDays: null
            },
            {
                code: 'BONUS25',
                points: 25,
                description: 'مكافأة إضافية للنشاط',
                type: 'bonus',
                icon: 'fas fa-plus',
                rarity: 'common',
                expiryDays: null
            },
            {
                code: 'GIFT75',
                points: 75,
                description: 'هدية خاصة من فريق Rix',
                type: 'gift',
                icon: 'fas fa-gift',
                rarity: 'uncommon',
                expiryDays: null
            },
            {
                code: 'MEGA200',
                points: 200,
                description: 'كود ميجا للمستخدمين المميزين',
                type: 'mega',
                icon: 'fas fa-star',
                rarity: 'rare',
                expiryDays: 7
            },
            {
                code: 'SUPER150',
                points: 150,
                description: 'كود سوبر محدود الوقت',
                type: 'super',
                icon: 'fas fa-rocket',
                rarity: 'uncommon',
                expiryDays: 3
            },
            {
                code: 'LUCKY300',
                points: 300,
                description: 'كود الحظ النادر جداً',
                type: 'lucky',
                icon: 'fas fa-clover',
                rarity: 'legendary',
                expiryDays: 1
            },
            {
                code: 'FRIEND30',
                points: 30,
                description: 'كود خاص للأصدقاء',
                type: 'friend',
                icon: 'fas fa-users',
                rarity: 'common',
                expiryDays: null
            },
            {
                code: 'TASK60',
                points: 60,
                description: 'مكافأة إكمال المهام',
                type: 'task',
                icon: 'fas fa-tasks',
                rarity: 'common',
                expiryDays: null
            },
            {
                code: 'VIP500',
                points: 500,
                description: 'كود VIP حصري للأعضاء المميزين',
                type: 'vip',
                icon: 'fas fa-crown',
                rarity: 'legendary',
                expiryDays: 1
            }
        ];
    }

    setupEventListeners() {
        // إدخال الكود بالضغط على Enter
        const codeInput = document.getElementById('promoCodeInput');
        if (codeInput) {
            codeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.usePromoCode();
                }
            });

            // تحويل النص لأحرف كبيرة تلقائياً
            codeInput.addEventListener('input', (e) => {
                e.target.value = e.target.value.toUpperCase();
            });
        }
    }

    updateUI() {
        // تحديث الرصيد
        document.getElementById('userBalance').textContent = `${rix.userBalance} روبكس`;

        // تحديث الإحصائيات
        document.getElementById('totalCodes').textContent = this.availableCodes.length;
        document.getElementById('usedCodes').textContent = this.userStats.usedCodes;
        document.getElementById('earnedFromCodes').textContent = this.userStats.earnedFromCodes;

        // تحديث شبكة الأكواد
        this.renderAvailableCodes();
        
        // تحديث سجل الأكواد
        this.renderCodesHistory();

        // إظهار مولد الأكواد للمستخدمين VIP
        if (rix.currentUser.isVIP || this.userStats.usedCodes >= 5) {
            document.getElementById('codeGenerator').style.display = 'block';
        }
    }

    renderAvailableCodes() {
        const codesGrid = document.getElementById('codesGrid');
        if (!codesGrid) return;

        codesGrid.innerHTML = this.availableCodes.map(code => {
            const isUsed = this.userCodesHistory.some(h => h.code === code.code);
            const isExpired = code.expiryDays && code.expiryDays < 1;
            
            return `
                <div class="code-card ${code.rarity} ${isUsed ? 'used' : ''} ${isExpired ? 'expired' : ''}">
                    <div class="code-header">
                        <div class="code-icon">
                            <i class="${code.icon}"></i>
                        </div>
                        <div class="code-rarity ${code.rarity}">
                            ${this.getRarityText(code.rarity)}
                        </div>
                    </div>
                    <div class="code-content">
                        <div class="code-value">${code.code}</div>
                        <div class="code-points">+${code.points} روبكس</div>
                        <p class="code-description">${code.description}</p>
                        ${code.expiryDays ? `
                            <div class="code-expiry">
                                <i class="fas fa-clock"></i>
                                ينتهي خلال ${code.expiryDays} أيام
                            </div>
                        ` : ''}
                    </div>
                    <div class="code-actions">
                        ${isUsed ? `
                            <button class="btn btn-secondary btn-full" disabled>
                                <i class="fas fa-check"></i> مستخدم
                            </button>
                        ` : isExpired ? `
                            <button class="btn btn-danger btn-full" disabled>
                                <i class="fas fa-times"></i> منتهي الصلاحية
                            </button>
                        ` : `
                            <button class="btn btn-primary btn-full" onclick="codesSystem.useSpecificCode('${code.code}')">
                                <i class="fas fa-gift"></i> استخدم الكود
                            </button>
                        `}
                    </div>
                </div>
            `;
        }).join('');
    }

    renderCodesHistory() {
        const historyContainer = document.getElementById('codesHistory');
        if (!historyContainer) return;

        if (this.userCodesHistory.length === 0) {
            historyContainer.innerHTML = `
                <div class="no-history">
                    <i class="fas fa-inbox"></i>
                    <h3>لم تستخدم أي أكواد بعد</h3>
                    <p>ابدأ باستخدام الأكواد المتاحة أعلاه</p>
                </div>
            `;
            return;
        }

        historyContainer.innerHTML = `
            <div class="history-list">
                ${this.userCodesHistory.map(history => `
                    <div class="history-item">
                        <div class="history-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="history-content">
                            <div class="history-code">${history.code}</div>
                            <div class="history-points">+${history.points} روبكس</div>
                            <div class="history-date">${new Date(history.date).toLocaleDateString('ar-SA')}</div>
                        </div>
                        <div class="history-status">
                            <i class="fas fa-check-circle"></i>
                            <span>مكتمل</span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    getRarityText(rarity) {
        const rarities = {
            'common': 'عادي',
            'uncommon': 'غير عادي',
            'rare': 'نادر',
            'legendary': 'أسطوري'
        };
        return rarities[rarity] || 'غير محدد';
    }

    usePromoCode() {
        const codeInput = document.getElementById('promoCodeInput');
        const code = codeInput.value.trim().toUpperCase();
        
        if (!code) {
            rix.showErrorMessage('يرجى إدخال الكود');
            return;
        }

        this.processCode(code);
        codeInput.value = '';
    }

    useSpecificCode(code) {
        this.processCode(code);
    }

    processCode(code) {
        // التحقق من استخدام الكود من قبل
        const isAlreadyUsed = this.userCodesHistory.some(h => h.code === code);
        if (isAlreadyUsed) {
            rix.showErrorMessage('لقد استخدمت هذا الكود من قبل');
            return;
        }

        // البحث في الأكواد المتاحة
        const availableCode = this.availableCodes.find(c => c.code === code);
        
        // البحث في أكواد الأدمن
        const adminData = localStorage.getItem('adminData');
        let adminCode = null;
        if (adminData) {
            const data = JSON.parse(adminData);
            adminCode = data.adminCodes?.find(c => 
                c.code === code && 
                c.isActive && 
                c.currentUses < c.maxUses &&
                (!c.expiryDate || new Date(c.expiryDate) > new Date())
            );
        }

        const foundCode = availableCode || adminCode;
        
        if (!foundCode) {
            rix.showErrorMessage('كود غير صحيح أو منتهي الصلاحية');
            return;
        }

        // تطبيق الكود
        const points = foundCode.points || foundCode.points;
        rix.userBalance += points;
        
        // تحديث الإحصائيات
        this.userStats.usedCodes++;
        this.userStats.earnedFromCodes += points;
        
        // إضافة للسجل
        this.userCodesHistory.unshift({
            code: code,
            points: points,
            date: new Date().toISOString()
        });

        // تحديث استخدام كود الأدمن
        if (adminCode) {
            adminCode.currentUses++;
            localStorage.setItem('adminData', JSON.stringify(JSON.parse(adminData)));
        }

        // حفظ البيانات
        rix.saveUserData();
        this.saveUserData();

        // تحديث الواجهة
        this.updateUI();

        // عرض رسالة نجاح
        rix.showSuccessMessage(`تم استخدام الكود بنجاح! حصلت على ${points} روبكس 🎉`);

        // تأثير بصري
        this.showCodeSuccessEffect(points);
    }

    showCodeSuccessEffect(points) {
        // إنشاء تأثير بصري للنجاح
        const effect = document.createElement('div');
        effect.className = 'code-success-effect';
        effect.innerHTML = `
            <div class="success-animation">
                <i class="fas fa-gift"></i>
                <span>+${points} روبكس</span>
            </div>
        `;
        
        document.body.appendChild(effect);
        
        setTimeout(() => {
            effect.remove();
        }, 3000);
    }

    useDailyCode() {
        const dailyCode = 'DAILY2024';
        const today = new Date().toDateString();
        const lastDailyUse = localStorage.getItem('lastDailyCode_' + rix.currentUser.username);
        
        if (lastDailyUse === today) {
            rix.showErrorMessage('لقد استخدمت الكود اليومي اليوم');
            return;
        }

        // استخدام الكود اليومي
        rix.userBalance += 100;
        this.userStats.usedCodes++;
        this.userStats.earnedFromCodes += 100;
        
        this.userCodesHistory.unshift({
            code: dailyCode,
            points: 100,
            date: new Date().toISOString()
        });

        localStorage.setItem('lastDailyCode_' + rix.currentUser.username, today);
        
        rix.saveUserData();
        this.saveUserData();
        this.updateUI();
        
        rix.showSuccessMessage('تم استخدام الكود اليومي! +100 روبكس 🎉');
        
        // تعطيل الزر
        const dailyButton = document.querySelector('#dailyCode button');
        if (dailyButton) {
            dailyButton.innerHTML = '<i class="fas fa-check"></i> مستخدم اليوم';
            dailyButton.disabled = true;
            dailyButton.classList.remove('btn-warning');
            dailyButton.classList.add('btn-secondary');
        }
    }

    generateCustomCode() {
        const points = parseInt(document.getElementById('customCodePoints').value);
        const prefix = document.getElementById('customCodePrefix').value || 'CUSTOM';
        
        if (!points || points < 10 || points > 500) {
            rix.showErrorMessage('يجب أن تكون النقاط بين 10 و 500');
            return;
        }

        if (rix.userBalance < points * 2) {
            rix.showErrorMessage(`تحتاج إلى ${points * 2} روبكس لإنشاء هذا الكود`);
            return;
        }

        // إنشاء كود مخصص
        const customCode = prefix.toUpperCase() + Math.random().toString(36).substr(2, 6).toUpperCase();
        
        // خصم التكلفة
        rix.userBalance -= points * 2;
        
        // إضافة الكود للأكواد المتاحة
        this.availableCodes.push({
            code: customCode,
            points: points,
            description: 'كود مخصص من المستخدم',
            type: 'custom',
            icon: 'fas fa-user',
            rarity: 'uncommon',
            expiryDays: 7
        });

        rix.saveUserData();
        this.updateUI();
        
        rix.showSuccessMessage(`تم إنشاء الكود المخصص: ${customCode}`);
        
        // مسح النموذج
        document.getElementById('customCodePoints').value = '';
        document.getElementById('customCodePrefix').value = '';
    }

    startTimers() {
        // تحديث مؤقت الكود اليومي
        setInterval(() => {
            this.updateDailyCodeTimer();
        }, 1000);
    }

    updateDailyCodeTimer() {
        const now = new Date();
        const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        const diff = tomorrow - now;
        
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        const timerElement = document.getElementById('dailyCodeTimer');
        if (timerElement) {
            timerElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
}

// دوال عامة
function usePromoCode() {
    codesSystem.usePromoCode();
}

function useDailyCode() {
    codesSystem.useDailyCode();
}

function generateCustomCode() {
    codesSystem.generateCustomCode();
}

function logout() {
    rix.logout();
}

// إنشاء نسخة من نظام الأكواد
const codesSystem = new CodesSystem();
