<?php
session_start();
require_once 'includes/db.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rix - اربح روبكس مجاناً</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <h1>🎮 Rix</h1>
                </div>
                <div class="nav-links">
                    <?php if (is_logged_in()): ?>
                        <a href="dashboard.php">لوحة التحكم</a>
                        <a href="earn.php">المهام</a>
                        <a href="withdraw.php">سحب روبكس</a>
                        <?php if (is_admin()): ?>
                            <a href="admin.php">الأدمن</a>
                        <?php endif; ?>
                        <a href="logout.php">تسجيل الخروج</a>
                    <?php else: ?>
                        <a href="login.php">تسجيل الدخول</a>
                        <a href="register.php">إنشاء حساب</a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>🚀 اربح روبكس مجاناً مع Rix!</h1>
                    <p>أكمل المهام البسيطة واحصل على نقاط لسحب روبكس حقيقي</p>
                    
                    <?php if (!is_logged_in()): ?>
                        <div class="cta-buttons">
                            <a href="register.php" class="btn btn-primary">ابدأ الآن مجاناً</a>
                            <a href="login.php" class="btn btn-secondary">لديك حساب؟</a>
                        </div>
                    <?php else: ?>
                        <?php 
                        $user_data = get_user_data($_SESSION['user_id']);
                        ?>
                        <div class="user-welcome">
                            <h2>مرحباً <?php echo htmlspecialchars($user_data['username']); ?>! 👋</h2>
                            <p>رصيدك الحالي: <span class="robux-count"><?php echo $user_data['robux_balance']; ?> روبكس</span></p>
                            <div class="cta-buttons">
                                <a href="earn.php" class="btn btn-primary">ابدأ المهام</a>
                                <a href="withdraw.php" class="btn btn-success">اسحب روبكس</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>

        <section class="features">
            <div class="container">
                <h2>✨ لماذا Rix؟</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>مهام سهلة</h3>
                        <p>مهام بسيطة وسريعة يمكن إكمالها في دقائق</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💎</div>
                        <h3>روبكس حقيقي</h3>
                        <p>احصل على روبكس حقيقي يتم تحويله لحسابك</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>دفع سريع</h3>
                        <p>طلبات السحب تتم مراجعتها خلال 24 ساعة</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3>آمن 100%</h3>
                        <p>موقع آمن ومضمون بدون مخاطر على حسابك</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="how-it-works">
            <div class="container">
                <h2>🔥 كيف يعمل؟</h2>
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3>سجل حساب</h3>
                        <p>إنشاء حساب مجاني في ثوانٍ</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3>أكمل المهام</h3>
                        <p>اختر من المهام المتاحة وأكملها</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3>اجمع النقاط</h3>
                        <p>احصل على نقاط مقابل كل مهمة</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3>اسحب روبكس</h3>
                        <p>حول نقاطك إلى روبكس حقيقي</p>
                    </div>
                </div>
            </div>
        </section>

        <?php if (is_logged_in()): ?>
        <section class="quick-stats">
            <div class="container">
                <h2>📊 إحصائياتك السريعة</h2>
                <div class="stats-grid">
                    <?php
                    $user_id = $_SESSION['user_id'];
                    
                    // عدد المهام المكتملة
                    $stmt = $pdo->prepare("SELECT COUNT(*) as completed_tasks FROM task_logs WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    $completed_tasks = $stmt->fetch()['completed_tasks'];
                    
                    // عدد طلبات السحب
                    $stmt = $pdo->prepare("SELECT COUNT(*) as withdraw_requests FROM withdraw_requests WHERE user_id = ?");
                    $stmt->execute([$user_id]);
                    $withdraw_requests = $stmt->fetch()['withdraw_requests'];
                    
                    // تاريخ الانضمام
                    $join_date = date('Y/m/d', strtotime($user_data['join_date']));
                    ?>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $user_data['robux_balance']; ?></div>
                        <div class="stat-label">روبكس متاح</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $completed_tasks; ?></div>
                        <div class="stat-label">مهام مكتملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $withdraw_requests; ?></div>
                        <div class="stat-label">طلبات سحب</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $join_date; ?></div>
                        <div class="stat-label">عضو منذ</div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
            <p>موقع آمن لربح الروبكس المجاني</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
</body>
</html>
