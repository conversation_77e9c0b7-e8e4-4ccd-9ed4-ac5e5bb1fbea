// نظام إدارة موقع Rix المتطور
class RixSystem {
    constructor() {
        this.currentUser = null;
        this.userBalance = 0;
        this.completedTasks = [];
        this.generatedCodes = [];
        this.inviteStats = { total: 0, active: 0, earnings: 0 };
        this.init();
    }

    init() {
        this.loadUserData();
        this.setupEventListeners();
        this.initAnimations();
        this.loadTasks();
        this.updateStats();
    }

    // تحميل بيانات المستخدم من localStorage
    loadUserData() {
        const userData = localStorage.getItem('rixUser');
        if (userData) {
            const user = JSON.parse(userData);
            this.currentUser = user;
            this.userBalance = user.balance || 0;
            this.completedTasks = user.completedTasks || [];
            this.generatedCodes = user.generatedCodes || [];
            this.inviteStats = user.inviteStats || { total: 0, active: 0, earnings: 0 };
            this.showDashboard();
        }
    }

    // حفظ بيانات المستخدم
    saveUserData() {
        if (this.currentUser) {
            this.currentUser.balance = this.userBalance;
            this.currentUser.completedTasks = this.completedTasks;
            this.currentUser.generatedCodes = this.generatedCodes;
            this.currentUser.inviteStats = this.inviteStats;
            localStorage.setItem('rixUser', JSON.stringify(this.currentUser));
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // التنقل السلس
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                if (link.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(link.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });

        // نماذج تسجيل الدخول والتسجيل
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // إغلاق النوافذ المنبثقة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    // تسجيل الدخول
    handleLogin(e) {
        e.preventDefault();
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        // محاكاة تسجيل الدخول
        if (username && password) {
            // التحقق من حساب الأدمن
            const isAdmin = username === 'admin' && password === 'admin123';

            this.currentUser = {
                username: username,
                balance: isAdmin ? 10000 : 50, // مكافأة تسجيل الدخول
                joinDate: new Date().toISOString(),
                completedTasks: [],
                generatedCodes: [],
                inviteStats: { total: 0, active: 0, earnings: 0 },
                isAdmin: isAdmin,
                level: 1,
                totalEarned: isAdmin ? 10000 : 50
            };

            this.userBalance = this.currentUser.balance;
            this.saveUserData();
            this.showSuccessMessage(isAdmin ? 'مرحباً أدمن! تم تسجيل الدخول بنجاح' : 'تم تسجيل الدخول بنجاح! حصلت على 50 روبكس مكافأة');
            this.closeModal('loginModal');

            // توجيه الأدمن للوحة الأدمن
            if (isAdmin) {
                window.location.href = 'admin.html';
            } else {
                this.showDashboard();
            }
        } else {
            this.showErrorMessage('يرجى ملء جميع الحقول');
        }
    }

    // التسجيل
    handleRegister(e) {
        e.preventDefault();
        const username = document.getElementById('registerUsername').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!username || !password || !confirmPassword) {
            this.showErrorMessage('يرجى ملء جميع الحقول');
            return;
        }

        if (password !== confirmPassword) {
            this.showErrorMessage('كلمات المرور غير متطابقة');
            return;
        }

        if (password.length < 6) {
            this.showErrorMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // إنشاء حساب جديد
        this.currentUser = {
            username: username,
            balance: 50, // مكافأة التسجيل
            joinDate: new Date().toISOString(),
            completedTasks: [],
            generatedCodes: [],
            inviteStats: { total: 0, active: 0, earnings: 0 }
        };

        this.userBalance = 50;
        this.saveUserData();
        this.showSuccessMessage('تم إنشاء الحساب بنجاح! حصلت على 50 روبكس مكافأة التسجيل');
        this.closeModal('registerModal');
        this.showDashboard();
    }

    // عرض لوحة التحكم
    showDashboard() {
        // تحديث شريط التنقل
        const navLinks = document.querySelector('.nav-links');
        if (this.currentUser && navLinks) {
            navLinks.innerHTML = `
                <a href="#home" class="nav-link">الرئيسية</a>
                <a href="#" class="nav-link" onclick="rix.showTasksPage()">المهام</a>
                <a href="#" class="nav-link" onclick="rix.showWithdrawPage()">السحب</a>
                <a href="#" class="nav-link" onclick="rix.showCodesPage()">الأكواد</a>
                <a href="#" class="nav-link" onclick="rix.showInvitePage()">الدعوات</a>
                <div class="user-balance">${this.userBalance} روبكس</div>
                <button class="btn btn-secondary" onclick="rix.logout()">تسجيل الخروج</button>
            `;
        }
    }

    // تسجيل الخروج
    logout() {
        this.currentUser = null;
        this.userBalance = 0;
        this.completedTasks = [];
        this.generatedCodes = [];
        this.inviteStats = { total: 0, active: 0, earnings: 0 };
        localStorage.removeItem('rixUser');
        location.reload();
    }

// تحميل المهام
loadTasks() {
    const tasks = [
        {
            id: 1,
            title: '🎥 مشاهدة فيديو ترحيبي',
            description: 'شاهد الفيديو الترحيبي للموقع لمدة دقيقة واحدة',
            reward: 15,
            type: 'video'
        },
        {
            id: 2,
            title: '👍 إعجاب بصفحة الفيسبوك',
            description: 'سجل إعجابك بصفحتنا الرسمية على الفيسبوك',
            reward: 25,
            type: 'social'
        },
        {
            id: 3,
            title: '🎮 انضمام لجروب Roblox',
            description: 'انضم إلى جروبنا المميز في Roblox (شرط للجيم باس)',
            reward: 40,
            type: 'group'
        },
        {
            id: 4,
            title: '📱 تحميل التطبيق',
            description: 'حمل تطبيقنا المجاني من متجر التطبيقات',
            reward: 50,
            type: 'app'
        },
        {
            id: 5,
            title: '📢 مشاركة مع الأصدقاء',
            description: 'شارك رابط الموقع مع 3 أصدقاء على الأقل',
            reward: 35,
            type: 'share'
        },
        {
            id: 6,
            title: '⭐ تقييم الموقع',
            description: 'قيم تجربتك على موقعنا وساعدنا في التحسين',
            reward: 30,
            type: 'review'
        },
        {
            id: 7,
            title: '🔔 تفعيل الإشعارات',
            description: 'فعل الإشعارات لتكون أول من يعلم بالمهام الجديدة',
            reward: 20,
            type: 'notification'
        },
        {
            id: 8,
            title: '📺 مشاهدة إعلان',
            description: 'شاهد إعلان قصير لمدة 30 ثانية لدعم الموقع',
            reward: 10,
            type: 'ad'
        }
    ];

    const tasksGrid = document.getElementById('tasksGrid');
    if (tasksGrid) {
        tasksGrid.innerHTML = tasks.map(task => `
            <div class="task-card ${this.completedTasks.includes(task.id) ? 'completed' : ''}">
                <div class="task-header">
                    <h3 class="task-title">${task.title}</h3>
                    <div class="task-reward">${task.reward} روبكس</div>
                </div>
                <p class="task-description">${task.description}</p>
                <button class="btn ${this.completedTasks.includes(task.id) ? 'btn-secondary' : 'btn-primary'} task-button"
                        onclick="rix.completeTask(${task.id}, ${task.reward})"
                        ${this.completedTasks.includes(task.id) ? 'disabled' : ''}>
                    ${this.completedTasks.includes(task.id) ? '✅ مكتملة' : '🚀 أكمل المهمة'}
                </button>
            </div>
        `).join('');
    }
}

// إكمال مهمة
completeTask(taskId, reward) {
    if (this.completedTasks.includes(taskId)) {
        this.showErrorMessage('لقد أكملت هذه المهمة من قبل');
        return;
    }

    // محاكاة إكمال المهمة
    this.completedTasks.push(taskId);
    this.userBalance += reward;
    this.saveUserData();

    this.showSuccessMessage(`تم إكمال المهمة بنجاح! حصلت على ${reward} روبكس`);
    this.loadTasks();
    this.showDashboard();
}

    // عرض صفحة الأكواد
    showCodesPage() {
        const content = `
            <div class="codes-section">
                <div class="container">
                    <div class="dashboard-header">
                        <h1><i class="fas fa-code"></i> إدارة الأكواد</h1>
                        <p>إنشاء أكواد مكافآت للمستخدمين</p>
                    </div>

                    <div class="code-generator">
                        <h3><i class="fas fa-plus-circle"></i> إنشاء كود جديد</h3>
                        <div class="code-input-group">
                            <input type="number" id="codePoints" placeholder="عدد النقاط" min="1" max="1000">
                            <input type="number" id="codeUses" placeholder="عدد الاستخدامات" min="1" max="100">
                            <button class="btn btn-primary" onclick="rix.generateCode()">
                                <i class="fas fa-magic"></i> إنشاء كود
                            </button>
                        </div>
                        <div id="generatedCodeDisplay"></div>
                    </div>

                    <div class="codes-list">
                        <h3><i class="fas fa-list"></i> الأكواد المُنشأة</h3>
                        <div id="codesListContainer">
                            ${this.renderCodesList()}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.innerHTML = this.getNavbar() + content + this.getFooter();
        this.setupEventListeners();
    }

    // إنشاء كود جديد
    generateCode() {
        const points = parseInt(document.getElementById('codePoints').value);
        const uses = parseInt(document.getElementById('codeUses').value);

        if (!points || !uses || points < 1 || uses < 1) {
            this.showErrorMessage('يرجى إدخال قيم صحيحة للنقاط والاستخدامات');
            return;
        }

        const code = this.generateRandomCode();
        const newCode = {
            id: Date.now(),
            code: code,
            points: points,
            maxUses: uses,
            currentUses: 0,
            createdAt: new Date().toISOString(),
            isActive: true
        };

        this.generatedCodes.push(newCode);
        this.saveUserData();

        document.getElementById('generatedCodeDisplay').innerHTML = `
            <div class="generated-code">
                <h4>تم إنشاء الكود بنجاح!</h4>
                <div class="code-value">${code}</div>
                <p>النقاط: ${points} | الاستخدامات: ${uses}</p>
            </div>
        `;

        document.getElementById('codesListContainer').innerHTML = this.renderCodesList();
        document.getElementById('codePoints').value = '';
        document.getElementById('codeUses').value = '';
    }

    // إنشاء كود عشوائي
    generateRandomCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = 'RIX';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // عرض قائمة الأكواد
    renderCodesList() {
        if (this.generatedCodes.length === 0) {
            return '<p style="text-align: center; opacity: 0.7;">لم يتم إنشاء أي أكواد بعد</p>';
        }

        return this.generatedCodes.map(code => `
            <div class="code-item">
                <div class="code-details">
                    <div class="code-value">${code.code}</div>
                    <div class="code-info">
                        النقاط: ${code.points} |
                        الاستخدامات: ${code.currentUses}/${code.maxUses} |
                        تاريخ الإنشاء: ${new Date(code.createdAt).toLocaleDateString('ar-SA')}
                    </div>
                </div>
                <div class="code-actions">
                    <button class="btn btn-warning btn-sm" onclick="rix.toggleCode(${code.id})">
                        ${code.isActive ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="rix.deleteCode(${code.id})">
                        حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    // تفعيل/إيقاف كود
    toggleCode(codeId) {
        const code = this.generatedCodes.find(c => c.id === codeId);
        if (code) {
            code.isActive = !code.isActive;
            this.saveUserData();
            document.getElementById('codesListContainer').innerHTML = this.renderCodesList();
            this.showSuccessMessage(`تم ${code.isActive ? 'تفعيل' : 'إيقاف'} الكود بنجاح`);
        }
    }

    // حذف كود
    deleteCode(codeId) {
        if (confirm('هل أنت متأكد من حذف هذا الكود؟')) {
            this.generatedCodes = this.generatedCodes.filter(c => c.id !== codeId);
            this.saveUserData();
            document.getElementById('codesListContainer').innerHTML = this.renderCodesList();
            this.showSuccessMessage('تم حذف الكود بنجاح');
        }
    }

    // عرض صفحة الدعوات
    showInvitePage() {
        const inviteLink = `${window.location.origin}?ref=${this.currentUser.username}`;

        const content = `
            <div class="invite-section">
                <div class="container">
                    <div class="dashboard-header">
                        <h1><i class="fas fa-users"></i> نظام الدعوات</h1>
                        <p>ادع أصدقائك واربح نقاط إضافية</p>
                    </div>

                    <div class="invite-link-generator">
                        <h3><i class="fas fa-link"></i> رابط الدعوة الخاص بك</h3>
                        <div class="invite-link">${inviteLink}</div>
                        <button class="btn btn-primary" onclick="rix.copyInviteLink('${inviteLink}')">
                            <i class="fas fa-copy"></i> نسخ الرابط
                        </button>
                        <p style="margin-top: 1rem; opacity: 0.8;">
                            احصل على 25 روبكس عن كل صديق يسجل باستخدام رابطك!
                        </p>
                    </div>

                    <div class="invite-stats">
                        <div class="invite-stat-card">
                            <div class="invite-stat-number">${this.inviteStats.total}</div>
                            <div class="invite-stat-label">إجمالي الدعوات</div>
                        </div>
                        <div class="invite-stat-card">
                            <div class="invite-stat-number">${this.inviteStats.active}</div>
                            <div class="invite-stat-label">دعوات نشطة</div>
                        </div>
                        <div class="invite-stat-card">
                            <div class="invite-stat-number">${this.inviteStats.earnings}</div>
                            <div class="invite-stat-label">روبكس مكتسب</div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <h3><i class="fas fa-gift"></i> مكافآت الدعوة</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <i class="fas fa-star" style="color: #4ecdc4;"></i>
                                25 روبكس عن كل صديق يسجل
                            </li>
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <i class="fas fa-star" style="color: #4ecdc4;"></i>
                                10 روبكس إضافية عندما يكمل صديقك أول مهمة
                            </li>
                            <li style="padding: 0.5rem 0;">
                                <i class="fas fa-star" style="color: #4ecdc4;"></i>
                                مكافأة خاصة عند الوصول لـ 10 دعوات نشطة
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        `;

        document.body.innerHTML = this.getNavbar() + content + this.getFooter();
        this.setupEventListeners();
    }

    // نسخ رابط الدعوة
    copyInviteLink(link) {
        navigator.clipboard.writeText(link).then(() => {
            this.showSuccessMessage('تم نسخ رابط الدعوة بنجاح!');
        }).catch(() => {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = link;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showSuccessMessage('تم نسخ رابط الدعوة بنجاح!');
        });
    }

    // عرض صفحة السحب
    showWithdrawPage() {
        const content = `
            <div class="withdraw-section">
                <div class="container">
                    <div class="dashboard-header">
                        <h1><i class="fas fa-money-bill-wave"></i> سحب المكافآت</h1>
                        <div class="user-balance">${this.userBalance} روبكس متاح</div>
                    </div>

                    <div class="withdraw-options">
                        <div class="withdraw-card robux-card">
                            <div class="withdraw-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <h3>سحب روبكس</h3>
                            <div class="withdraw-details">
                                <p><strong>الحد الأدنى:</strong> 100 روبكس</p>
                                <p><strong>وقت المعالجة:</strong> أقل من 24 ساعة</p>
                                <p><strong>الطريقة:</strong> تحويل مباشر للحساب</p>
                            </div>
                            <button class="btn btn-primary" onclick="rix.showWithdrawModal('robux')"
                                    ${this.userBalance < 100 ? 'disabled' : ''}>
                                ${this.userBalance < 100 ? 'رصيد غير كافي' : 'اسحب روبكس'}
                            </button>
                        </div>

                        <div class="withdraw-card gamepass-card">
                            <div class="withdraw-icon">
                                <i class="fas fa-gift"></i>
                            </div>
                            <h3>سحب جيم باس</h3>
                            <div class="withdraw-details">
                                <p><strong>المصدر:</strong> جروب Rix المميز</p>
                                <p><strong>الشرط:</strong> البقاء في الجروب 14 يوم</p>
                                <p><strong>وقت المعالجة:</strong> أقل من 24 ساعة</p>
                            </div>
                            <button class="btn btn-success" onclick="rix.showGamepassModal()">
                                اسحب جيم باس
                            </button>
                        </div>
                    </div>

                    <div class="withdraw-requirements">
                        <h3><i class="fas fa-exclamation-triangle"></i> شروط مهمة للسحب</h3>
                        <div class="requirements-grid">
                            <div class="requirement-item">
                                <i class="fas fa-users"></i>
                                <div>
                                    <h4>عضوية الجروب</h4>
                                    <p>يجب الانضمام لجروب Rix والبقاء فيه لمدة 14 يوم كاملة للحصول على الجيم باس</p>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <i class="fas fa-clock"></i>
                                <div>
                                    <h4>وقت المعالجة</h4>
                                    <p>جميع طلبات السحب تتم معالجتها في أقل من 24 ساعة مضمونة</p>
                                </div>
                            </div>
                            <div class="requirement-item">
                                <i class="fas fa-shield-check"></i>
                                <div>
                                    <h4>حساب نشط</h4>
                                    <p>يجب أن يكون حسابك نشط وقد أكملت المهام بشكل صحيح</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.innerHTML = this.getNavbar() + content + this.getFooter();
        this.setupEventListeners();
    }

    // عرض نافذة سحب الروبكس
    showWithdrawModal(type) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'withdrawModal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="rix.closeModal('withdrawModal')">&times;</span>
                <h2><i class="fas fa-coins"></i> سحب روبكس</h2>
                <form id="withdrawForm">
                    <div class="form-group">
                        <label for="withdrawAmount">مبلغ الروبكس</label>
                        <input type="number" id="withdrawAmount" min="100" max="${this.userBalance}"
                               placeholder="أدخل المبلغ (الحد الأدنى 100)" required>
                    </div>
                    <div class="form-group">
                        <label for="robloxUsername">اسم المستخدم في Roblox</label>
                        <input type="text" id="robloxUsername" placeholder="أدخل اسم المستخدم" required>
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-paper-plane"></i> إرسال طلب السحب
                    </button>
                </form>
                <p class="modal-footer">
                    سيتم معالجة طلبك خلال 24 ساعة كحد أقصى
                </p>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        document.getElementById('withdrawForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processWithdraw();
        });
    }

    // عرض نافذة سحب الجيم باس
    showGamepassModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'gamepassModal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="rix.closeModal('gamepassModal')">&times;</span>
                <h2><i class="fas fa-gift"></i> سحب جيم باس مجاني</h2>
                <div style="text-align: center; margin: 2rem 0;">
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 2rem; border-radius: 15px; border: 2px solid #4ecdc4;">
                        <h3 style="color: #4ecdc4; margin-bottom: 1rem;">شروط الحصول على الجيم باس</h3>
                        <ul style="list-style: none; padding: 0; text-align: right;">
                            <li style="padding: 0.5rem 0;">✅ الانضمام لجروب Rix في Roblox</li>
                            <li style="padding: 0.5rem 0;">✅ البقاء في الجروب لمدة 14 يوم كاملة</li>
                            <li style="padding: 0.5rem 0;">✅ إكمال 5 مهام على الأقل</li>
                            <li style="padding: 0.5rem 0;">✅ حساب نشط ومتفاعل</li>
                        </ul>
                    </div>
                </div>
                <form id="gamepassForm">
                    <div class="form-group">
                        <label for="gamepassUsername">اسم المستخدم في Roblox</label>
                        <input type="text" id="gamepassUsername" placeholder="أدخل اسم المستخدم" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="groupConfirm" required>
                            أؤكد أنني عضو في جروب Rix وسأبقى لمدة 14 يوم
                        </label>
                    </div>
                    <button type="submit" class="btn btn-success btn-full"
                            ${this.completedTasks.length < 5 ? 'disabled' : ''}>
                        <i class="fas fa-gift"></i>
                        ${this.completedTasks.length < 5 ? 'أكمل 5 مهام أولاً' : 'طلب جيم باس مجاني'}
                    </button>
                </form>
                <p class="modal-footer">
                    سيتم التحقق من عضويتك وإرسال الجيم باس خلال 24 ساعة
                </p>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        document.getElementById('gamepassForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processGamepassRequest();
        });
    }

    // معالجة طلب سحب الروبكس
    processWithdraw() {
        const amount = parseInt(document.getElementById('withdrawAmount').value);
        const username = document.getElementById('robloxUsername').value;

        if (amount < 100) {
            this.showErrorMessage('الحد الأدنى للسحب هو 100 روبكس');
            return;
        }

        if (amount > this.userBalance) {
            this.showErrorMessage('رصيدك غير كافي');
            return;
        }

        // خصم المبلغ من الرصيد
        this.userBalance -= amount;
        this.saveUserData();

        this.closeModal('withdrawModal');
        this.showSuccessMessage(`تم إرسال طلب سحب ${amount} روبكس بنجاح! سيتم التحويل خلال 24 ساعة`);
        this.showDashboard();
    }

    // معالجة طلب الجيم باس
    processGamepassRequest() {
        const username = document.getElementById('gamepassUsername').value;
        const confirmed = document.getElementById('groupConfirm').checked;

        if (!confirmed) {
            this.showErrorMessage('يجب تأكيد عضوية الجروب');
            return;
        }

        this.closeModal('gamepassModal');
        this.showSuccessMessage('تم إرسال طلب الجيم باس بنجاح! سيتم التحقق من عضويتك وإرسال الجيم باس خلال 24 ساعة');
    }

    // عرض نافذة استخدام الكود
    showUseCodeModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'useCodeModal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="rix.closeModal('useCodeModal')">&times;</span>
                <h2><i class="fas fa-gift"></i> استخدام كود مكافأة</h2>
                <form id="useCodeForm">
                    <div class="form-group">
                        <label for="promoCode">أدخل الكود</label>
                        <input type="text" id="promoCode" placeholder="مثال: RIX123ABC" required style="text-transform: uppercase;">
                    </div>
                    <button type="submit" class="btn btn-success btn-full">
                        <i class="fas fa-check"></i> استخدام الكود
                    </button>
                </form>
                <p class="modal-footer">
                    الأكواد حساسة للأحرف الكبيرة والصغيرة
                </p>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        document.getElementById('useCodeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.usePromoCode();
        });
    }

    // استخدام كود المكافأة
    usePromoCode() {
        const code = document.getElementById('promoCode').value.toUpperCase();

        if (!code) {
            this.showErrorMessage('يرجى إدخال الكود');
            return;
        }

        // البحث عن الكود في الأكواد المُنشأة من الأدمن
        const adminData = localStorage.getItem('adminData');
        if (adminData) {
            const data = JSON.parse(adminData);
            const adminCodes = data.adminCodes || [];

            const foundCode = adminCodes.find(c =>
                c.code === code &&
                c.isActive &&
                c.currentUses < c.maxUses &&
                (!c.expiryDate || new Date(c.expiryDate) > new Date())
            );

            if (foundCode) {
                // التحقق من عدم استخدام المستخدم للكود من قبل
                const usedCodes = this.currentUser.usedCodes || [];
                if (usedCodes.includes(code)) {
                    this.showErrorMessage('لقد استخدمت هذا الكود من قبل');
                    return;
                }

                // إضافة النقاط للمستخدم
                this.userBalance += foundCode.points;
                foundCode.currentUses++;

                // حفظ الكود كمستخدم
                if (!this.currentUser.usedCodes) {
                    this.currentUser.usedCodes = [];
                }
                this.currentUser.usedCodes.push(code);

                // حفظ البيانات
                this.saveUserData();
                localStorage.setItem('adminData', JSON.stringify(data));

                this.closeModal('useCodeModal');
                this.showSuccessMessage(`تم استخدام الكود بنجاح! حصلت على ${foundCode.points} روبكس`);
                this.showDashboard();
                return;
            }
        }

        // أكواد افتراضية للاختبار
        const defaultCodes = {
            'RIX100': { points: 100, used: false },
            'WELCOME50': { points: 50, used: false },
            'BONUS25': { points: 25, used: false },
            'GIFT75': { points: 75, used: false }
        };

        if (defaultCodes[code]) {
            const usedCodes = this.currentUser.usedCodes || [];
            if (usedCodes.includes(code)) {
                this.showErrorMessage('لقد استخدمت هذا الكود من قبل');
                return;
            }

            this.userBalance += defaultCodes[code].points;
            if (!this.currentUser.usedCodes) {
                this.currentUser.usedCodes = [];
            }
            this.currentUser.usedCodes.push(code);

            this.saveUserData();
            this.closeModal('useCodeModal');
            this.showSuccessMessage(`تم استخدام الكود بنجاح! حصلت على ${defaultCodes[code].points} روبكس`);
            this.showDashboard();
        } else {
            this.showErrorMessage('كود غير صحيح أو منتهي الصلاحية');
        }
    }

    // عرض صفحة المهام
    showTasksPage() {
        const content = `
            <div class="tasks-section">
                <div class="container">
                    <div class="dashboard-header">
                        <h1><i class="fas fa-tasks"></i> المهام المتاحة</h1>
                        <div class="user-balance">${this.userBalance} روبكس</div>
                    </div>
                    <div class="tasks-grid" id="tasksGrid">
                        <!-- سيتم ملء المهام هنا -->
                    </div>
                    <div class="tasks-info">
                        <div class="info-card">
                            <h3><i class="fas fa-info-circle"></i> معلومات مهمة</h3>
                            <ul>
                                <li>✅ جميع المهام آمنة ومضمونة</li>
                                <li>✅ يمكن إكمال كل مهمة مرة واحدة فقط</li>
                                <li>✅ النقاط تضاف فوراً بعد إكمال المهمة</li>
                                <li>✅ مهام جديدة تضاف يومياً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.innerHTML = this.getNavbar() + content + this.getFooter();
        this.loadTasks();
        this.setupEventListeners();
    }

    // الحصول على شريط التنقل
    getNavbar() {
        return `
            <nav class="navbar">
                <div class="container">
                    <div class="nav-brand">
                        <h1><i class="fas fa-gamepad"></i> Rix</h1>
                    </div>
                    <div class="nav-links">
                        ${this.currentUser ? `
                            <a href="#" class="nav-link" onclick="location.reload()">الرئيسية</a>
                            <a href="#" class="nav-link" onclick="rix.showTasksPage()">المهام</a>
                            <a href="#" class="nav-link" onclick="rix.showWithdrawPage()">السحب</a>
                            <a href="#" class="nav-link" onclick="rix.showCodesPage()">الأكواد</a>
                            <a href="#" class="nav-link" onclick="rix.showInvitePage()">الدعوات</a>
                            <div class="user-balance">${this.userBalance} روبكس</div>
                            <button class="btn btn-secondary" onclick="rix.logout()">تسجيل الخروج</button>
                        ` : `
                            <a href="#home" class="nav-link active">الرئيسية</a>
                            <a href="#features" class="nav-link">المميزات</a>
                            <a href="#tasks" class="nav-link">المهام</a>
                            <a href="#withdraw" class="nav-link">السحب</a>
                            <a href="#" class="btn btn-primary" onclick="rix.showLoginModal()">تسجيل الدخول</a>
                        `}
                    </div>
                    <div class="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </div>
                </div>
            </nav>
        `;
    }

    // الحصول على التذييل
    getFooter() {
        return `
            <footer class="footer">
                <div class="container">
                    <div class="footer-content">
                        <div class="footer-section">
                            <h3><i class="fas fa-gamepad"></i> Rix</h3>
                            <p>موقع آمن ومضمون لربح الروبكس والجيم باس المجاني</p>
                            <div class="social-links">
                                <a href="#"><i class="fab fa-discord"></i></a>
                                <a href="#"><i class="fab fa-youtube"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                            </div>
                        </div>
                        <div class="footer-section">
                            <h4>روابط سريعة</h4>
                            <ul>
                                <li><a href="#home">الرئيسية</a></li>
                                <li><a href="#features">المميزات</a></li>
                                <li><a href="#tasks">المهام</a></li>
                                <li><a href="#withdraw">السحب</a></li>
                            </ul>
                        </div>
                        <div class="footer-section">
                            <h4>إحصائيات</h4>
                            <div class="footer-stats">
                                <div class="footer-stat">
                                    <span class="stat-number">15K+</span>
                                    <span class="stat-label">مستخدم</span>
                                </div>
                                <div class="footer-stat">
                                    <span class="stat-number">50K+</span>
                                    <span class="stat-label">روبكس مدفوع</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="footer-bottom">
                        <p>&copy; 2024 Rix - جميع الحقوق محفوظة | موقع آمن لربح الروبكس والجيم باس</p>
                    </div>
                </div>
            </footer>
        `;
    }

    // عرض النوافذ المنبثقة
    showLoginModal() {
        this.showModal('loginModal');
    }

    showRegisterModal() {
        this.closeModal('loginModal');
        this.showModal('registerModal');
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            if (modalId === 'withdrawModal' || modalId === 'gamepassModal') {
                modal.remove();
            }
        }
    }

    // رسائل النجاح والخطأ
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
            padding: 1rem 2rem;
            border-radius: 15px;
            color: white;
            font-weight: 600;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 4000);
    }

    // تحديث الإحصائيات
    updateStats() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(stat => {
            const target = parseInt(stat.getAttribute('data-target'));
            if (target) {
                this.animateCounter(stat, target);
            }
        });
    }

    // تحريك العدادات
    animateCounter(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                element.textContent = target.toLocaleString();
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 20);
    }

    // تهيئة الرسوم المتحركة
    initAnimations() {
        // تأثير الظهور التدريجي
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // تطبيق التأثير على العناصر
        document.querySelectorAll('.feature-card, .task-card, .withdraw-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    }
}

// إنشاء نسخة من النظام
const rix = new RixSystem();

// دوال عامة للوصول من HTML
function showLoginModal() {
    rix.showLoginModal();
}

function showRegisterModal() {
    rix.showRegisterModal();
}

function showUseCodeModal() {
    rix.showUseCodeModal();
}

function closeModal(modalId) {
    rix.closeModal(modalId);
}

function showWithdrawModal(type) {
    rix.showWithdrawModal(type);
}

function showGamepassModal() {
    rix.showGamepassModal();
}

function checkLogin(page) {
    if (!rix.currentUser) {
        rix.showErrorMessage('يجب تسجيل الدخول أولاً للوصول لهذه الصفحة');
        rix.showLoginModal();
        return false;
    }
    window.location.href = page;
    return true;
}

function toggleMobileMenu() {
    const navLinks = document.getElementById('navLinks');
    navLinks.classList.toggle('mobile-active');
}

document.addEventListener('DOMContentLoaded', function() {
    // إظهار شاشة التحميل
    showLoadingScreen();

    // تحميل الموقع بعد ثانية واحدة
    setTimeout(() => {
        hideLoadingScreen();
        initializeWebsite();
    }, 1500);

    // إخفاء شاشة التحميل في حالة عدم اختفائها
    setTimeout(() => {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }, 3000);
});

function showLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.style.display = 'flex';
        loadingScreen.style.opacity = '1';

        // محاكاة تقدم التحميل
        let progress = 0;
        const progressBar = document.getElementById('loadingProgress');
        const loadingText = document.getElementById('loadingText');

        const loadingSteps = [
            'تحميل الموارد...',
            'تهيئة النظام...',
            'تحميل البيانات...',
            'تطبيق التأثيرات...',
            'اكتمل التحميل!'
        ];

        const progressInterval = setInterval(() => {
            progress += Math.random() * 25;
            if (progress > 100) progress = 100;

            if (progressBar) {
                progressBar.style.width = progress + '%';
            }

            if (loadingText) {
                const stepIndex = Math.floor((progress / 100) * (loadingSteps.length - 1));
                loadingText.textContent = loadingSteps[stepIndex];
            }

            if (progress >= 100) {
                clearInterval(progressInterval);
                setTimeout(() => {
                    hideLoadingScreen();
                }, 500);
            }
        }, 200);
    }
}

function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }
}

function initializeWebsite() {
    // تحديث الإحصائيات عند تحميل الصفحة
    rix.updateStats();

    // التحقق من رابط الدعوة
    const urlParams = new URLSearchParams(window.location.search);
    const referrer = urlParams.get('ref');
    if (referrer && !rix.currentUser) {
        localStorage.setItem('referrer', referrer);
        rix.showSuccessMessage(`تم دعوتك من قبل ${referrer}! سجل حساب جديد للحصول على مكافأة إضافية`);
    }

    // تطبيق تأثيرات الرسوم المتحركة
    rix.initAnimations();

    // إعداد التنقل السلس
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // إعداد القائمة المحمولة
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    if (mobileToggle && navLinks) {
        mobileToggle.addEventListener('click', () => {
            navLinks.classList.toggle('mobile-active');
        });
    }

    // إغلاق النوافذ المنبثقة عند النقر خارجها
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            rix.closeModal(e.target.id);
        }
    });

    // تأثيرات الأزرار المحسنة
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // تأثير الكتابة للعنوان الرئيسي
    const heroTitle = document.querySelector('.gradient-text');
    if (heroTitle && !rix.currentUser) {
        const text = heroTitle.textContent;
        heroTitle.textContent = '';
        let i = 0;

        const typeWriter = () => {
            if (i < text.length) {
                heroTitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };

        setTimeout(typeWriter, 500);
    }

    // تحديث الوقت كل ثانية
    setInterval(() => {
        const timeElements = document.querySelectorAll('.current-time');
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA');
        timeElements.forEach(el => {
            el.textContent = timeString;
        });
    }, 1000);

    // حفظ البيانات تلقائياً كل 30 ثانية
    setInterval(() => {
        if (rix.currentUser) {
            rix.saveUserData();
        }
    }, 30000);

    // تأثيرات الجسيمات في الخلفية
    createParticles();

    // تأثيرات التمرير
    setupScrollEffects();

    // تحديث شريط التنقل عند التمرير
    setupNavbarScroll();
}

// إنشاء تأثير الجسيمات
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
    `;

    document.body.appendChild(particlesContainer);

    for (let i = 0; i < 50; i++) {
        createParticle(particlesContainer);
    }
}

function createParticle(container) {
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: absolute;
        width: 2px;
        height: 2px;
        background: rgba(78, 205, 196, 0.5);
        border-radius: 50%;
        animation: float ${Math.random() * 10 + 5}s linear infinite;
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
    `;

    container.appendChild(particle);

    // إزالة الجسيم وإنشاء واحد جديد
    setTimeout(() => {
        particle.remove();
        createParticle(container);
    }, (Math.random() * 10 + 5) * 1000);
}

// دوال مساعدة إضافية
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
    } else {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

// دوال التأثيرات الإضافية
function setupScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // تطبيق التأثير على العناصر
    document.querySelectorAll('.feature-card, .stat-item, .step, .withdraw-card').forEach(el => {
        el.classList.add('animate-element');
        observer.observe(el);
    });
}

function setupNavbarScroll() {
    const navbar = document.getElementById('navbar');
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        if (currentScrollY > lastScrollY && currentScrollY > 200) {
            navbar.classList.add('hidden');
        } else {
            navbar.classList.remove('hidden');
        }

        lastScrollY = currentScrollY;
    });
}

function createAdvancedParticles() {
    const particlesContainer = document.getElementById('particles');
    if (!particlesContainer) return;

    for (let i = 0; i < 30; i++) {
        createFloatingParticle(particlesContainer);
    }
}

function createFloatingParticle(container) {
    const particle = document.createElement('div');
    const size = Math.random() * 4 + 2;
    const duration = Math.random() * 20 + 10;
    const delay = Math.random() * 5;

    particle.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        background: radial-gradient(circle, rgba(78, 205, 196, 0.8), rgba(69, 183, 209, 0.4));
        border-radius: 50%;
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
        animation: floatParticle ${duration}s linear infinite;
        animation-delay: ${delay}s;
        pointer-events: none;
        z-index: 1;
    `;

    container.appendChild(particle);

    // إزالة الجسيم وإنشاء واحد جديد
    setTimeout(() => {
        particle.remove();
        createFloatingParticle(container);
    }, (duration + delay) * 1000);
}

// إضافة CSS للرسوم المتحركة
const animationStyles = document.createElement('style');
animationStyles.textContent = `
    @keyframes floatParticle {
        0% {
            transform: translateY(100vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100px) rotate(360deg);
            opacity: 0;
        }
    }

    .animate-element {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s ease;
    }

    .animate-element.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .navbar.scrolled {
        background: rgba(15, 15, 35, 0.98);
        backdrop-filter: blur(25px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }

    .navbar.hidden {
        transform: translateY(-100%);
    }

    .navbar {
        transition: all 0.3s ease;
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
`;
document.head.appendChild(animationStyles);

// دوال إضافية للتفاعل
function addInteractiveEffects() {
    // تأثير المؤشر المخصص
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.style.cssText = `
        position: fixed;
        width: 20px;
        height: 20px;
        background: radial-gradient(circle, rgba(78, 205, 196, 0.8), transparent);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        transition: transform 0.1s ease;
    `;
    document.body.appendChild(cursor);

    document.addEventListener('mousemove', (e) => {
        cursor.style.left = e.clientX - 10 + 'px';
        cursor.style.top = e.clientY - 10 + 'px';
    });

    // تأثير النقر
    document.addEventListener('click', (e) => {
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: rgba(78, 205, 196, 0.6);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9998;
            left: ${e.clientX - 5}px;
            top: ${e.clientY - 5}px;
            animation: rippleEffect 0.6s ease-out;
        `;

        document.body.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
}

// إضافة CSS للتأثيرات التفاعلية
const interactiveStyles = document.createElement('style');
interactiveStyles.textContent = `
    @keyframes rippleEffect {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(20);
            opacity: 0;
        }
    }

    .feature-card, .stat-item, .btn {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .feature-card:hover {
        transform: translateY(-10px) scale(1.02);
    }

    .stat-item:hover {
        transform: translateY(-5px);
    }

    body {
        cursor: none;
    }

    a, button, .clickable {
        cursor: none;
    }

    a:hover ~ .custom-cursor,
    button:hover ~ .custom-cursor,
    .clickable:hover ~ .custom-cursor {
        transform: scale(1.5);
        background: radial-gradient(circle, rgba(255, 107, 107, 0.8), transparent);
    }
`;
document.head.appendChild(interactiveStyles);

// تصدير النظام للاستخدام العام
window.rix = rix;
window.showLoginModal = showLoginModal;
window.showRegisterModal = showRegisterModal;
window.showUseCodeModal = showUseCodeModal;
window.closeModal = closeModal;
window.showWithdrawModal = showWithdrawModal;
window.showGamepassModal = showGamepassModal;
window.checkLogin = checkLogin;
window.toggleMobileMenu = toggleMobileMenu;

// تشغيل التأثيرات التفاعلية
setTimeout(() => {
    addInteractiveEffects();
    createAdvancedParticles();
}, 1000);
    // تأثير التمرير السلس للروابط
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // تطبيق التأثير على البطاقات والعناصر
    const animatedElements = document.querySelectorAll('.feature-card, .stat-card, .task-card, .action-card, .content-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // عداد الأرقام المتحرك
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        }
        
        updateCounter();
    }

    // تطبيق عداد الأرقام على الإحصائيات
    const statNumbers = document.querySelectorAll('.stat-number');
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.textContent);
                if (!isNaN(target) && target > 0) {
                    animateCounter(entry.target, target);
                }
                statsObserver.unobserve(entry.target);
            }
        });
    });

    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });

    // تأثير النقر على الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إنشاء تأثير الموجة
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // تحديث الوقت في الصفحات
    function updateTime() {
        const timeElements = document.querySelectorAll('.current-time');
        const now = new Date();
        const timeString = now.toLocaleString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        timeElements.forEach(el => {
            el.textContent = timeString;
        });
    }

    // تحديث الوقت كل دقيقة
    updateTime();
    setInterval(updateTime, 60000);

    // تأكيد العمليات المهمة
    const dangerButtons = document.querySelectorAll('.btn-danger');
    dangerButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!this.hasAttribute('onclick')) {
                e.preventDefault();
                if (confirm('هل أنت متأكد من هذا الإجراء؟')) {
                    // إذا كان الزر داخل نموذج، أرسل النموذج
                    const form = this.closest('form');
                    if (form) {
                        form.submit();
                    }
                }
            }
        });
    });

    // تحسين تجربة النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '⏳ جاري المعالجة...';
                
                // إعادة تفعيل الزر بعد 3 ثوان في حالة عدم إعادة التوجيه
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = submitButton.getAttribute('data-original-text') || 'إرسال';
                }, 3000);
            }
        });
        
        // حفظ النص الأصلي للأزرار
        const submitButtons = form.querySelectorAll('button[type="submit"]');
        submitButtons.forEach(button => {
            button.setAttribute('data-original-text', button.innerHTML);
        });
    });

    // تحسين حقول الإدخال
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        // تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim() !== '') {
                this.parentElement.classList.add('has-value');
            } else {
                this.parentElement.classList.remove('has-value');
            }
        });
        
        // فحص القيم الموجودة عند التحميل
        if (input.value.trim() !== '') {
            input.parentElement.classList.add('has-value');
        }
    });

    // إخفاء/إظهار كلمة المرور
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    passwordInputs.forEach(input => {
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.innerHTML = '👁️';
        toggleButton.className = 'password-toggle';
        toggleButton.style.cssText = `
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2rem;
        `;
        
        input.parentElement.style.position = 'relative';
        input.style.paddingLeft = '40px';
        input.parentElement.appendChild(toggleButton);
        
        toggleButton.addEventListener('click', function() {
            if (input.type === 'password') {
                input.type = 'text';
                this.innerHTML = '🙈';
            } else {
                input.type = 'password';
                this.innerHTML = '👁️';
            }
        });
    });

    // تحديث عداد الروبكس بتأثير متحرك
    function updateRobuxDisplay(newAmount, element) {
        if (!element) return;
        
        const currentAmount = parseInt(element.textContent) || 0;
        const difference = newAmount - currentAmount;
        
        if (difference !== 0) {
            const duration = 1000;
            const steps = 30;
            const stepValue = difference / steps;
            let currentStep = 0;
            
            const interval = setInterval(() => {
                currentStep++;
                const displayValue = Math.round(currentAmount + (stepValue * currentStep));
                element.textContent = displayValue;
                
                if (currentStep >= steps) {
                    clearInterval(interval);
                    element.textContent = newAmount;
                }
            }, duration / steps);
        }
    }

    // مراقبة تغييرات الروبكس (للاستخدام مع AJAX في المستقبل)
    window.updateRobuxBalance = updateRobuxDisplay;

    // تحسين الجداول على الشاشات الصغيرة
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-scroll-wrapper';
        wrapper.style.cssText = 'overflow-x: auto; -webkit-overflow-scrolling: touch;';
        
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });

    // تأثير التحميل للصفحة
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
    });

    // إضافة تأثير الريبل للأزرار
    const style = document.createElement('style');
    style.textContent = `
        .btn {
            position: relative;
            overflow: hidden;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .form-group.focused label {
            color: #667eea;
        }
        
        .form-group.focused input,
        .form-group.focused textarea {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        body {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        body.loaded {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
});

// دوال مساعدة عامة
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 2rem;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        ${type === 'success' ? 'background: #28a745;' : 'background: #dc3545;'}
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// تصدير الدوال للاستخدام العام
window.showNotification = showNotification;
