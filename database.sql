-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS cdwvgbvox_rix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cdwvgbvox_rix;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    join_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_admin TINYINT(1) DEFAULT 0,
    robux_balance INT DEFAULT 0,
    INDEX idx_username (username),
    INDEX idx_is_admin (is_admin)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المهام
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    reward INT NOT NULL,
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل المهام
CREATE TABLE IF NOT EXISTS task_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_task (user_id, task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_completed_at (completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول طلبات السحب
CREATE TABLE IF NOT EXISTS withdraw_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    robux_amount INT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإعدادات
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT IGNORE INTO settings (setting_key, setting_value) VALUES 
('withdraw_wait_hours', '24'),
('group_name', 'Rix Robux Group'),
('min_withdraw', '100'),
('site_title', 'Rix - اربح روبكس مجاناً'),
('max_withdraw', '10000'),
('daily_task_limit', '10');

-- إدراج مهام تجريبية
INSERT IGNORE INTO tasks (title, description, reward, is_active) VALUES 
('مشاهدة فيديو قصير', 'شاهد فيديو لمدة 30 ثانية واحصل على نقاط', 10, 1),
('تسجيل الإعجاب بصفحة', 'سجل إعجابك بصفحتنا على وسائل التواصل', 25, 1),
('مشاركة المنشور', 'شارك منشورنا مع أصدقائك', 30, 1),
('تحميل التطبيق', 'حمل تطبيقنا المجاني من المتجر', 50, 1),
('دعوة صديق', 'ادع صديقاً للانضمام إلى الموقع', 75, 1),
('إكمال الاستبيان', 'أكمل استبيان قصير عن تجربتك', 40, 1),
('متابعة الحساب', 'تابع حسابنا الرسمي', 20, 1),
('مشاهدة الإعلان', 'شاهد إعلان قصير لمدة 15 ثانية', 15, 1),
('تقييم التطبيق', 'قيم تطبيقنا في المتجر', 35, 1),
('الاشتراك في القناة', 'اشترك في قناتنا على يوتيوب', 45, 1);

-- إنشاء مستخدم أدمن تجريبي (كلمة المرور: admin123)
-- يمكنك تغيير اسم المستخدم وكلمة المرور
INSERT IGNORE INTO users (username, password, is_admin, robux_balance) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 0);

-- إنشاء مستخدمين تجريبيين (كلمة المرور: user123)
INSERT IGNORE INTO users (username, password, is_admin, robux_balance) VALUES 
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 150),
('user2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 75),
('user3', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 200);

-- إدراج بعض سجلات المهام التجريبية
INSERT IGNORE INTO task_logs (user_id, task_id) VALUES 
(2, 1), (2, 2), (2, 3),
(3, 1), (3, 4),
(4, 1), (4, 2), (4, 3), (4, 4), (4, 5);

-- إدراج طلبات سحب تجريبية
INSERT IGNORE INTO withdraw_requests (user_id, robux_amount, status) VALUES 
(2, 100, 'pending'),
(4, 150, 'paid');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_users_join_date ON users(join_date);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_withdraw_processed_at ON withdraw_requests(processed_at);

-- إنشاء عرض (View) لإحصائيات سريعة
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.robux_balance,
    u.join_date,
    COUNT(DISTINCT tl.id) as completed_tasks,
    COUNT(DISTINCT wr.id) as total_withdrawals,
    SUM(CASE WHEN wr.status = 'paid' THEN wr.robux_amount ELSE 0 END) as total_withdrawn
FROM users u
LEFT JOIN task_logs tl ON u.id = tl.user_id
LEFT JOIN withdraw_requests wr ON u.id = wr.user_id
WHERE u.is_admin = 0
GROUP BY u.id, u.username, u.robux_balance, u.join_date;

-- إنشاء إجراء مخزن لتنظيف البيانات القديمة
DELIMITER //
CREATE PROCEDURE CleanOldData()
BEGIN
    -- حذف سجلات المهام الأقدم من 6 أشهر
    DELETE FROM task_logs 
    WHERE completed_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);
    
    -- حذف طلبات السحب المدفوعة الأقدم من سنة
    DELETE FROM withdraw_requests 
    WHERE status = 'paid' AND processed_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
END //
DELIMITER ;

-- إنشاء حدث لتشغيل التنظيف التلقائي شهرياً
-- CREATE EVENT IF NOT EXISTS monthly_cleanup
-- ON SCHEDULE EVERY 1 MONTH
-- DO CALL CleanOldData();

-- إنشاء دالة لحساب إجمالي النقاط المكتسبة
DELIMITER //
CREATE FUNCTION GetUserTotalEarned(user_id INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE total_earned INT DEFAULT 0;
    
    SELECT COALESCE(SUM(t.reward), 0) INTO total_earned
    FROM task_logs tl
    JOIN tasks t ON tl.task_id = t.id
    WHERE tl.user_id = user_id;
    
    RETURN total_earned;
END //
DELIMITER ;

-- إنشاء مشغل (Trigger) لتحديث الرصيد عند إكمال المهام
DELIMITER //
CREATE TRIGGER update_balance_after_task
AFTER INSERT ON task_logs
FOR EACH ROW
BEGIN
    DECLARE task_reward INT;
    
    SELECT reward INTO task_reward 
    FROM tasks 
    WHERE id = NEW.task_id;
    
    UPDATE users 
    SET robux_balance = robux_balance + task_reward 
    WHERE id = NEW.user_id;
END //
DELIMITER ;

-- إنشاء مشغل لتسجيل وقت معالجة طلبات السحب
DELIMITER //
CREATE TRIGGER update_processed_time
BEFORE UPDATE ON withdraw_requests
FOR EACH ROW
BEGIN
    IF OLD.status = 'pending' AND NEW.status = 'paid' THEN
        SET NEW.processed_at = NOW();
    END IF;
END //
DELIMITER ;

-- عرض ملخص قاعدة البيانات
SELECT 'Database setup completed successfully!' as Status;
SELECT COUNT(*) as Total_Users FROM users;
SELECT COUNT(*) as Total_Tasks FROM tasks;
SELECT COUNT(*) as Total_Settings FROM settings;
