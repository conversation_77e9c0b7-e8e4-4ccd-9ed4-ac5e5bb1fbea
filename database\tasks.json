{"tasks": [{"id": 1, "title": "تابع صفحتنا على فيسبوك", "description": "تابع صفحة Rix الرسمية على فيسبوك واحصل على آخر الأخبار والأكواد الحصرية", "reward": 25, "type": "social", "difficulty": "easy", "icon": "fab fa-facebook", "link": "https://facebook.com/rix.official", "duration": 60, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T10:00:00.000Z", "completedBy": [2, 3, 4, 5], "totalCompletions": 4}, {"id": 2, "title": "انضم لقناة التليجرام", "description": "انضم لقناة Rix على التليجرام للحصول على أكواد حصرية يومية ومسابقات مربحة", "reward": 30, "type": "social", "difficulty": "easy", "icon": "fab fa-telegram", "link": "https://t.me/rix_official", "duration": 60, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T10:15:00.000Z", "completedBy": [2, 3, 4], "totalCompletions": 3}, {"id": 3, "title": "تابعنا على تويتر", "description": "تابع حساب Rix على تويتر وأعد تغريد المنشور المثبت للحصول على المكافأة", "reward": 35, "type": "social", "difficulty": "medium", "icon": "fab fa-twitter", "link": "https://twitter.com/rix_official", "duration": 120, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T10:30:00.000Z", "completedBy": [2, 3, 5], "totalCompletions": 3}, {"id": 4, "title": "شاهد فيديو تعريفي عن Roblox", "description": "شاهد فيديو مدته 5 دقائق عن أساسيات لعبة Roblox وكيفية ربح الروبكس", "reward": 40, "type": "video", "difficulty": "easy", "icon": "fas fa-play-circle", "link": "https://youtube.com/watch?v=dQw4w9WgXcQ", "duration": 300, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T11:00:00.000Z", "completedBy": [3, 4], "totalCompletions": 2}, {"id": 5, "title": "زيارة موقع شريك", "description": "زر موقع شريكنا واقضِ دقيقتين في تصفح المحتوى للحصول على المكافأة", "reward": 45, "type": "website", "difficulty": "easy", "icon": "fas fa-globe", "link": "https://example.com/partner", "duration": 120, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T11:30:00.000Z", "completedBy": [2, 3], "totalCompletions": 2}, {"id": 6, "title": "حمل تطبيق Roblox", "description": "حمل تطبيق Roblox الرسمي من متجر التطبيقات وافتحه لمدة دقيقة", "reward": 50, "type": "app", "difficulty": "easy", "icon": "fas fa-download", "link": "https://play.google.com/store/apps/details?id=com.roblox.client", "duration": 60, "requireLogin": true, "trackTime": false, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T12:00:00.000Z", "completedBy": [3, 4], "totalCompletions": 2}, {"id": 7, "title": "انضم لجروب Roblox المميز", "description": "انضم لجروب Rix في Roblox واحصل على مكافآت حصرية وجيم باس مجاني", "reward": 60, "type": "group", "difficulty": "medium", "icon": "fas fa-users", "link": "https://www.roblox.com/groups/12345678/Rix-Official", "duration": 180, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T12:30:00.000Z", "completedBy": [3], "totalCompletions": 1}, {"id": 8, "title": "استبيان عن ألعابك المفضلة", "description": "أجب على استبيان قصير عن ألعابك المفضلة في Roblox لمساعدتنا في التحسين", "reward": 35, "type": "survey", "difficulty": "easy", "icon": "fas fa-clipboard-list", "link": "https://forms.google.com/survey123", "duration": 180, "requireLogin": true, "trackTime": true, "isActive": true, "target": "all", "createdBy": "admin", "createdAt": "2024-12-01T13:00:00.000Z", "completedBy": [2, 3, 4], "totalCompletions": 3}, {"id": 9, "title": "مهمة خاصة للمستوى المتقدم", "description": "مهمة حصرية للمستخدمين من المستوى 3 فما فوق - زيارة موقع متقدم", "reward": 80, "type": "custom", "difficulty": "hard", "icon": "fas fa-star", "link": "https://advanced.example.com", "duration": 300, "requireLogin": true, "trackTime": true, "isActive": true, "target": "level", "requiredLevel": 3, "createdBy": "admin", "createdAt": "2024-12-15T10:00:00.000Z", "completedBy": [3], "totalCompletions": 1}, {"id": 10, "title": "مهمة شخصية لـ testuser", "description": "مهمة خاصة مخصصة للمستخدم testuser - زيارة رابط حصري", "reward": 100, "type": "custom", "difficulty": "medium", "icon": "fas fa-gift", "link": "https://special.example.com/testuser", "duration": 240, "requireLogin": true, "trackTime": true, "isActive": true, "target": "specific", "specificUser": "testuser", "createdBy": "admin", "createdAt": "2024-12-20T14:00:00.000Z", "completedBy": [], "totalCompletions": 0}], "dailyTasks": [{"id": "daily_login", "title": "تسجيل الدخول اليومي", "description": "سجل دخولك يومياً واحصل على مكافأة", "reward": 15, "type": "daily", "icon": "fas fa-sign-in-alt", "resetTime": "00:00"}, {"id": "daily_share", "title": "مشاركة يومية", "description": "شارك الموقع مع الأصدقاء", "reward": 20, "type": "daily", "icon": "fas fa-share-alt", "resetTime": "00:00"}, {"id": "daily_ads", "title": "مشاهدة إعلانات", "description": "شاهد 3 إعلانات قصيرة", "reward": 25, "type": "daily", "icon": "fas fa-play-circle", "resetTime": "00:00"}, {"id": "daily_quiz", "title": "اختبار يومي", "description": "أ<PERSON><PERSON> على 5 أسئلة بسيطة", "reward": 30, "type": "daily", "icon": "fas fa-question-circle", "resetTime": "00:00"}], "stats": {"totalTasks": 10, "activeTasks": 10, "totalCompletions": 21, "averageReward": 50, "mostPopularTask": 1, "lastUpdated": "2024-12-29T12:00:00.000Z"}}