<?php
session_start();
require_once 'includes/db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

$user_data = get_user_data($_SESSION['user_id']);
$user_id = $_SESSION['user_id'];

// جلب إحصائيات المستخدم
$stmt = $pdo->prepare("SELECT COUNT(*) as completed_tasks FROM task_logs WHERE user_id = ?");
$stmt->execute([$user_id]);
$completed_tasks = $stmt->fetch()['completed_tasks'];

$stmt = $pdo->prepare("SELECT COUNT(*) as pending_withdrawals FROM withdraw_requests WHERE user_id = ? AND status = 'pending'");
$stmt->execute([$user_id]);
$pending_withdrawals = $stmt->fetch()['pending_withdrawals'];

$stmt = $pdo->prepare("SELECT COUNT(*) as total_withdrawals FROM withdraw_requests WHERE user_id = ?");
$stmt->execute([$user_id]);
$total_withdrawals = $stmt->fetch()['total_withdrawals'];

// جلب آخر المهام المكتملة
$stmt = $pdo->prepare("
    SELECT t.title, t.reward, tl.completed_at 
    FROM task_logs tl 
    JOIN tasks t ON tl.task_id = t.id 
    WHERE tl.user_id = ? 
    ORDER BY tl.completed_at DESC 
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_tasks = $stmt->fetchAll();

// جلب آخر طلبات السحب
$stmt = $pdo->prepare("
    SELECT robux_amount, status, created_at 
    FROM withdraw_requests 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 5
");
$stmt->execute([$user_id]);
$recent_withdrawals = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="dashboard.php" class="active">لوحة التحكم</a>
                    <a href="earn.php">المهام</a>
                    <a href="withdraw.php">سحب روبكس</a>
                    <?php if (is_admin()): ?>
                        <a href="admin.php">الأدمن</a>
                    <?php endif; ?>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="dashboard">
            <div class="container">
                <div class="dashboard-header">
                    <h1>مرحباً <?php echo htmlspecialchars($user_data['username']); ?>! 👋</h1>
                    <p>إليك نظرة سريعة على حسابك</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">💎</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $user_data['robux_balance']; ?></div>
                            <div class="stat-label">روبكس متاح</div>
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $completed_tasks; ?></div>
                            <div class="stat-label">مهام مكتملة</div>
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $pending_withdrawals; ?></div>
                            <div class="stat-label">طلبات معلقة</div>
                        </div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $total_withdrawals; ?></div>
                            <div class="stat-label">إجمالي السحوبات</div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h2>🚀 إجراءات سريعة</h2>
                    <div class="actions-grid">
                        <a href="earn.php" class="action-card">
                            <div class="action-icon">🎯</div>
                            <h3>ابدأ المهام</h3>
                            <p>أكمل مهام جديدة واربح نقاط</p>
                        </a>
                        <a href="withdraw.php" class="action-card">
                            <div class="action-icon">💰</div>
                            <h3>اسحب روبكس</h3>
                            <p>حول نقاطك إلى روبكس حقيقي</p>
                        </a>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="content-grid">
                        <div class="content-card">
                            <h3>📋 آخر المهام المكتملة</h3>
                            <?php if (empty($recent_tasks)): ?>
                                <div class="empty-state">
                                    <p>لم تكمل أي مهام بعد</p>
                                    <a href="earn.php" class="btn btn-primary">ابدأ أول مهمة</a>
                                </div>
                            <?php else: ?>
                                <div class="tasks-list">
                                    <?php foreach ($recent_tasks as $task): ?>
                                        <div class="task-item">
                                            <div class="task-info">
                                                <h4><?php echo htmlspecialchars($task['title']); ?></h4>
                                                <span class="task-date"><?php echo date('Y/m/d H:i', strtotime($task['completed_at'])); ?></span>
                                            </div>
                                            <div class="task-reward">+<?php echo $task['reward']; ?> روبكس</div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="content-card">
                            <h3>💸 آخر طلبات السحب</h3>
                            <?php if (empty($recent_withdrawals)): ?>
                                <div class="empty-state">
                                    <p>لم تطلب أي سحوبات بعد</p>
                                    <a href="withdraw.php" class="btn btn-success">اطلب سحب</a>
                                </div>
                            <?php else: ?>
                                <div class="withdrawals-list">
                                    <?php foreach ($recent_withdrawals as $withdrawal): ?>
                                        <div class="withdrawal-item">
                                            <div class="withdrawal-info">
                                                <h4><?php echo $withdrawal['robux_amount']; ?> روبكس</h4>
                                                <span class="withdrawal-date"><?php echo date('Y/m/d H:i', strtotime($withdrawal['created_at'])); ?></span>
                                            </div>
                                            <div class="withdrawal-status status-<?php echo $withdrawal['status']; ?>">
                                                <?php 
                                                echo $withdrawal['status'] == 'pending' ? 'معلق' : 'مدفوع';
                                                ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="account-info">
                    <h3>👤 معلومات الحساب</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">اسم المستخدم:</span>
                            <span class="info-value"><?php echo htmlspecialchars($user_data['username']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الانضمام:</span>
                            <span class="info-value"><?php echo date('Y/m/d', strtotime($user_data['join_date'])); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">نوع الحساب:</span>
                            <span class="info-value"><?php echo $user_data['is_admin'] ? 'أدمن' : 'مستخدم عادي'; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
</body>
</html>
