<?php
session_start();
require_once 'includes/db.php';

// التحقق من صلاحيات الأدمن
if (!is_admin()) {
    header('Location: index.php');
    exit();
}

$message = '';
$error = '';

// معالجة تحديث حالة طلب السحب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_withdrawal'])) {
    $request_id = (int)$_POST['request_id'];
    $new_status = $_POST['status'];
    
    if (in_array($new_status, ['pending', 'paid'])) {
        $stmt = $pdo->prepare("UPDATE withdraw_requests SET status = ? WHERE id = ?");
        if ($stmt->execute([$new_status, $request_id])) {
            $message = "تم تحديث حالة الطلب بنجاح";
        } else {
            $error = "حدث خطأ أثناء تحديث الطلب";
        }
    }
}

// معالجة إضافة مهمة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_task'])) {
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $reward = (int)$_POST['reward'];
    
    if (empty($title) || empty($description) || $reward <= 0) {
        $error = "جميع حقول المهمة مطلوبة والمكافأة يجب أن تكون أكبر من صفر";
    } else {
        $stmt = $pdo->prepare("INSERT INTO tasks (title, description, reward) VALUES (?, ?, ?)");
        if ($stmt->execute([$title, $description, $reward])) {
            $message = "تم إضافة المهمة بنجاح";
        } else {
            $error = "حدث خطأ أثناء إضافة المهمة";
        }
    }
}

// معالجة تحديث المهمة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_task'])) {
    $task_id = (int)$_POST['task_id'];
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $reward = (int)$_POST['reward'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    $stmt = $pdo->prepare("UPDATE tasks SET title = ?, description = ?, reward = ?, is_active = ? WHERE id = ?");
    if ($stmt->execute([$title, $description, $reward, $is_active, $task_id])) {
        $message = "تم تحديث المهمة بنجاح";
    } else {
        $error = "حدث خطأ أثناء تحديث المهمة";
    }
}

// معالجة حذف المهمة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_task'])) {
    $task_id = (int)$_POST['task_id'];
    
    $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
    if ($stmt->execute([$task_id])) {
        $message = "تم حذف المهمة بنجاح";
    } else {
        $error = "حدث خطأ أثناء حذف المهمة";
    }
}

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_settings'])) {
    $withdraw_wait_hours = (int)$_POST['withdraw_wait_hours'];
    $group_name = clean_input($_POST['group_name']);
    $min_withdraw = (int)$_POST['min_withdraw'];
    
    $settings = [
        'withdraw_wait_hours' => $withdraw_wait_hours,
        'group_name' => $group_name,
        'min_withdraw' => $min_withdraw
    ];
    
    foreach ($settings as $key => $value) {
        $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
        $stmt->execute([$value, $key]);
    }
    
    $message = "تم تحديث الإعدادات بنجاح";
}

// جلب طلبات السحب
$stmt = $pdo->prepare("
    SELECT wr.*, u.username 
    FROM withdraw_requests wr 
    JOIN users u ON wr.user_id = u.id 
    ORDER BY wr.created_at DESC
");
$stmt->execute();
$withdrawal_requests = $stmt->fetchAll();

// جلب المهام
$stmt = $pdo->prepare("SELECT * FROM tasks ORDER BY id DESC");
$stmt->execute();
$tasks = $stmt->fetchAll();

// جلب الإعدادات
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings");
$stmt->execute();
$settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// جلب إحصائيات عامة
$stmt = $pdo->prepare("SELECT COUNT(*) as total_users FROM users");
$stmt->execute();
$total_users = $stmt->fetch()['total_users'];

$stmt = $pdo->prepare("SELECT COUNT(*) as total_tasks FROM tasks");
$stmt->execute();
$total_tasks = $stmt->fetch()['total_tasks'];

$stmt = $pdo->prepare("SELECT COUNT(*) as pending_withdrawals FROM withdraw_requests WHERE status = 'pending'");
$stmt->execute();
$pending_withdrawals = $stmt->fetch()['pending_withdrawals'];

$stmt = $pdo->prepare("SELECT SUM(robux_amount) as total_paid FROM withdraw_requests WHERE status = 'paid'");
$stmt->execute();
$total_paid = $stmt->fetch()['total_paid'] ?? 0;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الأدمن - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="dashboard.php">لوحة التحكم</a>
                    <a href="earn.php">المهام</a>
                    <a href="withdraw.php">سحب روبكس</a>
                    <a href="admin.php" class="active">الأدمن</a>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="admin-section">
            <div class="container">
                <div class="admin-header">
                    <h1>⚙️ لوحة تحكم الأدمن</h1>
                    <p>إدارة الموقع والمستخدمين</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        ✅ <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        ❌ <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات عامة -->
                <div class="admin-stats">
                    <h3>📊 إحصائيات عامة</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">👥</div>
                            <div class="stat-number"><?php echo $total_users; ?></div>
                            <div class="stat-label">إجمالي المستخدمين</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-number"><?php echo $total_tasks; ?></div>
                            <div class="stat-label">إجمالي المهام</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-icon">⏳</div>
                            <div class="stat-number"><?php echo $pending_withdrawals; ?></div>
                            <div class="stat-label">طلبات معلقة</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-icon">💰</div>
                            <div class="stat-number"><?php echo $total_paid; ?></div>
                            <div class="stat-label">روبكس مدفوع</div>
                        </div>
                    </div>
                </div>

                <!-- طلبات السحب -->
                <div class="admin-section-card">
                    <h3>🧾 طلبات السحب</h3>
                    <?php if (empty($withdrawal_requests)): ?>
                        <p>لا توجد طلبات سحب</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($withdrawal_requests as $request): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($request['username']); ?></td>
                                            <td><?php echo $request['robux_amount']; ?> روبكس</td>
                                            <td><?php echo date('Y/m/d H:i', strtotime($request['created_at'])); ?></td>
                                            <td>
                                                <span class="status status-<?php echo $request['status']; ?>">
                                                    <?php echo $request['status'] == 'pending' ? 'معلق' : 'مدفوع'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($request['status'] == 'pending'): ?>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                                        <input type="hidden" name="status" value="paid">
                                                        <button type="submit" name="update_withdrawal" class="btn btn-sm btn-success"
                                                                onclick="return confirm('هل تم دفع هذا الطلب؟')">
                                                            ✅ تم الدفع
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <span class="text-success">✅ مدفوع</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- إدارة المهام -->
                <div class="admin-section-card">
                    <h3>🎯 إدارة المهام</h3>

                    <!-- إضافة مهمة جديدة -->
                    <div class="add-task-form">
                        <h4>إضافة مهمة جديدة</h4>
                        <form method="POST" class="admin-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="title">عنوان المهمة</label>
                                    <input type="text" id="title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="reward">المكافأة (روبكس)</label>
                                    <input type="number" id="reward" name="reward" min="1" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="description">وصف المهمة</label>
                                <textarea id="description" name="description" rows="3" required></textarea>
                            </div>
                            <button type="submit" name="add_task" class="btn btn-primary">إضافة المهمة</button>
                        </form>
                    </div>

                    <!-- قائمة المهام -->
                    <div class="tasks-list">
                        <h4>المهام الموجودة</h4>
                        <?php if (empty($tasks)): ?>
                            <p>لا توجد مهام</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="admin-table">
                                    <thead>
                                        <tr>
                                            <th>العنوان</th>
                                            <th>المكافأة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($tasks as $task): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($task['title']); ?></td>
                                                <td><?php echo $task['reward']; ?> روبكس</td>
                                                <td>
                                                    <span class="status status-<?php echo $task['is_active'] ? 'active' : 'inactive'; ?>">
                                                        <?php echo $task['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <button onclick="editTask(<?php echo htmlspecialchars(json_encode($task)); ?>)"
                                                            class="btn btn-sm btn-warning">تعديل</button>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                        <button type="submit" name="delete_task" class="btn btn-sm btn-danger"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذه المهمة؟')">
                                                            حذف
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- الإعدادات -->
                <div class="admin-section-card">
                    <h3>⚙️ إعدادات الموقع</h3>
                    <form method="POST" class="admin-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="withdraw_wait_hours">وقت انتظار السحب (ساعة)</label>
                                <input type="number" id="withdraw_wait_hours" name="withdraw_wait_hours"
                                       value="<?php echo $settings['withdraw_wait_hours'] ?? 24; ?>" min="1" required>
                            </div>
                            <div class="form-group">
                                <label for="min_withdraw">الحد الأدنى للسحب (روبكس)</label>
                                <input type="number" id="min_withdraw" name="min_withdraw"
                                       value="<?php echo $settings['min_withdraw'] ?? 100; ?>" min="1" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="group_name">اسم الجروب</label>
                            <input type="text" id="group_name" name="group_name"
                                   value="<?php echo htmlspecialchars($settings['group_name'] ?? 'اسم الجروب'); ?>" required>
                        </div>
                        <button type="submit" name="update_settings" class="btn btn-success">حفظ الإعدادات</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- نافذة تعديل المهمة -->
    <div id="editTaskModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h3>تعديل المهمة</h3>
            <form method="POST" id="editTaskForm">
                <input type="hidden" id="edit_task_id" name="task_id">
                <div class="form-group">
                    <label for="edit_title">عنوان المهمة</label>
                    <input type="text" id="edit_title" name="title" required>
                </div>
                <div class="form-group">
                    <label for="edit_description">وصف المهمة</label>
                    <textarea id="edit_description" name="description" rows="3" required></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_reward">المكافأة (روبكس)</label>
                        <input type="number" id="edit_reward" name="reward" min="1" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="edit_is_active" name="is_active">
                            المهمة نشطة
                        </label>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="submit" name="update_task" class="btn btn-success">حفظ التغييرات</button>
                    <button type="button" onclick="closeEditModal()" class="btn btn-secondary">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
    <script>
        function editTask(task) {
            document.getElementById('edit_task_id').value = task.id;
            document.getElementById('edit_title').value = task.title;
            document.getElementById('edit_description').value = task.description;
            document.getElementById('edit_reward').value = task.reward;
            document.getElementById('edit_is_active').checked = task.is_active == 1;
            document.getElementById('editTaskModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editTaskModal').style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            var modal = document.getElementById('editTaskModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
