// نظام إدارة قاعدة البيانات المؤقتة
class DatabaseManager {
    constructor() {
        this.baseUrl = './database/';
        this.cache = {};
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
        this.init();
    }

    init() {
        console.log('🗄️ تم تهيئة نظام قاعدة البيانات');
        this.loadInitialData();
    }

    // تحميل البيانات الأولية
    async loadInitialData() {
        try {
            // محاولة تحميل البيانات من الملفات
            const results = await Promise.allSettled([
                this.loadUsers(),
                this.loadTasks(),
                this.loadCodes(),
                this.loadWithdrawals(),
                this.loadStats()
            ]);

            // التحقق من نجاح التحميل
            const failed = results.filter(r => r.status === 'rejected');
            if (failed.length > 0) {
                console.warn(`⚠️ فشل تحميل ${failed.length} من أصل ${results.length} ملفات`);
                this.fallbackToLocalStorage();
            } else {
                console.log('✅ تم تحميل جميع البيانات بنجاح');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.fallbackToLocalStorage();
        }
    }

    // تحميل المستخدمين
    async loadUsers() {
        try {
            const response = await fetch(this.baseUrl + 'users.json');
            if (response.ok) {
                const data = await response.json();
                this.cache.users = {
                    data: data,
                    timestamp: Date.now()
                };
                return data;
            }
        } catch (error) {
            console.warn('تعذر تحميل بيانات المستخدمين، استخدام البيانات المحلية');
        }
        return this.getDefaultUsers();
    }

    // تحميل المهام
    async loadTasks() {
        try {
            const response = await fetch(this.baseUrl + 'tasks.json');
            if (response.ok) {
                const data = await response.json();
                this.cache.tasks = {
                    data: data,
                    timestamp: Date.now()
                };
                return data;
            }
        } catch (error) {
            console.warn('تعذر تحميل بيانات المهام، استخدام البيانات المحلية');
        }
        return this.getDefaultTasks();
    }

    // تحميل الأكواد
    async loadCodes() {
        try {
            const response = await fetch(this.baseUrl + 'codes.json');
            if (response.ok) {
                const data = await response.json();
                this.cache.codes = {
                    data: data,
                    timestamp: Date.now()
                };
                return data;
            }
        } catch (error) {
            console.warn('تعذر تحميل بيانات الأكواد، استخدام البيانات المحلية');
        }
        return this.getDefaultCodes();
    }

    // تحميل طلبات السحب
    async loadWithdrawals() {
        try {
            const response = await fetch(this.baseUrl + 'withdrawals.json');
            if (response.ok) {
                const data = await response.json();
                this.cache.withdrawals = {
                    data: data,
                    timestamp: Date.now()
                };
                return data;
            }
        } catch (error) {
            console.warn('تعذر تحميل بيانات السحب، استخدام البيانات المحلية');
        }
        return this.getDefaultWithdrawals();
    }

    // تحميل الإحصائيات
    async loadStats() {
        try {
            const response = await fetch(this.baseUrl + 'stats.json');
            if (response.ok) {
                const data = await response.json();
                this.cache.stats = {
                    data: data,
                    timestamp: Date.now()
                };
                return data;
            }
        } catch (error) {
            console.warn('تعذر تحميل الإحصائيات، استخدام البيانات المحلية');
        }
        return this.getDefaultStats();
    }

    // الحصول على البيانات مع التحقق من الكاش
    async getData(type) {
        const cached = this.cache[type];
        
        // التحقق من صحة الكاش
        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            return cached.data;
        }

        // إعادة تحميل البيانات
        switch (type) {
            case 'users':
                return await this.loadUsers();
            case 'tasks':
                return await this.loadTasks();
            case 'codes':
                return await this.loadCodes();
            case 'withdrawals':
                return await this.loadWithdrawals();
            case 'stats':
                return await this.loadStats();
            default:
                throw new Error('نوع بيانات غير صحيح');
        }
    }

    // البحث عن مستخدم
    async findUser(username, password = null) {
        const usersData = await this.getData('users');
        const user = usersData.users.find(u => u.username === username);
        
        if (user && password && user.password !== password) {
            return null;
        }
        
        return user;
    }

    // البحث عن مستخدم بالـ ID
    async findUserById(id) {
        const usersData = await this.getData('users');
        return usersData.users.find(u => u.id === id);
    }

    // الحصول على المهام للمستخدم
    async getUserTasks(username, userLevel = 1) {
        const tasksData = await this.getData('tasks');
        return tasksData.tasks.filter(task => {
            if (!task.isActive) return false;
            
            if (task.target === 'all') return true;
            if (task.target === 'specific' && task.specificUser === username) return true;
            if (task.target === 'level' && userLevel >= (task.requiredLevel || 1)) return true;
            
            return false;
        });
    }

    // الحصول على الأكواد المتاحة
    async getAvailableCodes() {
        const codesData = await this.getData('codes');
        return codesData.codes.filter(code => 
            code.isActive && 
            code.currentUses < code.maxUses &&
            (!code.expiryDate || new Date(code.expiryDate) > new Date())
        );
    }

    // التحقق من صحة الكود
    async validateCode(codeString, username) {
        const codesData = await this.getData('codes');
        
        // البحث في الأكواد العادية
        let code = codesData.codes.find(c => 
            c.code === codeString && 
            c.isActive && 
            c.currentUses < c.maxUses &&
            (!c.expiryDate || new Date(c.expiryDate) > new Date()) &&
            !c.usedBy.includes(username)
        );

        // البحث في أكواد الأدمن
        if (!code && codesData.adminCodes) {
            code = codesData.adminCodes.find(c => 
                c.code === codeString && 
                c.isActive && 
                c.currentUses < c.maxUses &&
                (!c.expiryDate || new Date(c.expiryDate) > new Date())
            );
        }

        return code;
    }

    // الحصول على طلبات السحب للمستخدم
    async getUserWithdrawals(username) {
        const withdrawalsData = await this.getData('withdrawals');
        return withdrawalsData.withdrawals.filter(w => w.username === username);
    }

    // الحصول على الإحصائيات العامة
    async getGlobalStats() {
        const statsData = await this.getData('stats');
        return statsData.globalStats;
    }

    // حفظ البيانات (محاكاة)
    async saveData(type, data) {
        // في التطبيق الحقيقي، سيتم إرسال البيانات للخادم
        console.log(`💾 حفظ بيانات ${type}:`, data);
        
        // تحديث الكاش
        this.cache[type] = {
            data: data,
            timestamp: Date.now()
        };
        
        // حفظ في localStorage كنسخة احتياطية
        localStorage.setItem(`db_${type}`, JSON.stringify(data));
        
        return true;
    }

    // الرجوع للتخزين المحلي في حالة الفشل
    fallbackToLocalStorage() {
        console.log('🔄 الرجوع للتخزين المحلي...');
        
        const types = ['users', 'tasks', 'codes', 'withdrawals', 'stats'];
        types.forEach(type => {
            const saved = localStorage.getItem(`db_${type}`);
            if (saved) {
                this.cache[type] = {
                    data: JSON.parse(saved),
                    timestamp: Date.now()
                };
            } else {
                this.cache[type] = {
                    data: this.getDefaultData(type),
                    timestamp: Date.now()
                };
            }
        });
    }

    // الحصول على البيانات الافتراضية
    getDefaultData(type) {
        switch (type) {
            case 'users':
                return this.getDefaultUsers();
            case 'tasks':
                return this.getDefaultTasks();
            case 'codes':
                return this.getDefaultCodes();
            case 'withdrawals':
                return this.getDefaultWithdrawals();
            case 'stats':
                return this.getDefaultStats();
            default:
                return {};
        }
    }

    // بيانات افتراضية للمستخدمين
    getDefaultUsers() {
        return {
            users: [
                {
                    id: 1,
                    username: "admin",
                    password: "admin123",
                    balance: 10000,
                    isAdmin: true,
                    isVIP: true,
                    level: 10,
                    joinDate: new Date().toISOString()
                }
            ],
            stats: {
                totalUsers: 1,
                activeUsers: 1
            }
        };
    }

    // بيانات افتراضية للمهام
    getDefaultTasks() {
        return {
            tasks: [],
            dailyTasks: [],
            stats: {
                totalTasks: 0,
                activeTasks: 0
            }
        };
    }

    // بيانات افتراضية للأكواد
    getDefaultCodes() {
        return {
            codes: [
                {
                    id: 1,
                    code: "RIX100",
                    points: 100,
                    description: "كود ترحيبي",
                    type: "welcome",
                    rarity: "common",
                    maxUses: 1000,
                    currentUses: 0,
                    isActive: true,
                    usedBy: []
                }
            ],
            adminCodes: [],
            stats: {
                totalCodes: 1,
                activeCodes: 1
            }
        };
    }

    // بيانات افتراضية لطلبات السحب
    getDefaultWithdrawals() {
        return {
            withdrawals: [],
            stats: {
                totalWithdrawals: 0,
                completedWithdrawals: 0
            }
        };
    }

    // بيانات افتراضية للإحصائيات
    getDefaultStats() {
        return {
            globalStats: {
                totalUsers: 1,
                activeUsers: 1,
                totalRobuxDistributed: 0,
                totalTasksCompleted: 0
            }
        };
    }

    // تنظيف الكاش
    clearCache() {
        this.cache = {};
        console.log('🧹 تم تنظيف الكاش');
    }

    // إعادة تحميل البيانات
    async refresh() {
        this.clearCache();
        await this.loadInitialData();
        console.log('🔄 تم إعادة تحميل البيانات');
    }
}

// إنشاء نسخة من مدير قاعدة البيانات
const db = new DatabaseManager();

// تصدير للاستخدام العام
window.db = db;
