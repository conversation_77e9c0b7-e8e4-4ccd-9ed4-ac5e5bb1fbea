<?php
// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'cdwvgbvox_rix';
$username = 'CS';
$password = '@!47XKiDzxmpm3ta';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دالة لتنظيف البيانات
function clean_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة للتحقق من تسجيل الدخول
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

// دالة للتحقق من صلاحيات الأدمن
function is_admin() {
    global $pdo;
    if (!is_logged_in()) return false;
    
    $stmt = $pdo->prepare("SELECT is_admin FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    return $user && $user['is_admin'] == 1;
}

// دالة لجلب بيانات المستخدم
function get_user_data($user_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

// دالة لتحديث رصيد الروبكس
function update_robux_balance($user_id, $amount) {
    global $pdo;
    $stmt = $pdo->prepare("UPDATE users SET robux_balance = robux_balance + ? WHERE id = ?");
    return $stmt->execute([$amount, $user_id]);
}

// دالة لإنشاء الجداول إذا لم تكن موجودة
function create_tables() {
    global $pdo;
    
    // جدول المستخدمين
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        join_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_admin TINYINT(1) DEFAULT 0,
        robux_balance INT DEFAULT 0
    )");
    
    // جدول المهام
    $pdo->exec("CREATE TABLE IF NOT EXISTS tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        reward INT NOT NULL,
        description TEXT,
        is_active TINYINT(1) DEFAULT 1
    )");
    
    // جدول سجل المهام
    $pdo->exec("CREATE TABLE IF NOT EXISTS task_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        task_id INT NOT NULL,
        completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (task_id) REFERENCES tasks(id)
    )");
    
    // جدول طلبات السحب
    $pdo->exec("CREATE TABLE IF NOT EXISTS withdraw_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        robux_amount INT NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    
    // جدول الإعدادات
    $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(50) UNIQUE NOT NULL,
        setting_value TEXT
    )");
    
    // إدراج الإعدادات الافتراضية
    $pdo->exec("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES 
        ('withdraw_wait_hours', '24'),
        ('group_name', 'اسم الجروب'),
        ('min_withdraw', '100')");
}

// إنشاء الجداول عند تحميل الملف
create_tables();
?>
