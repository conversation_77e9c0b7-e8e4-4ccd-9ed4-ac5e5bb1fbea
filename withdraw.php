<?php
session_start();
require_once 'includes/db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    header('Location: login.php');
    exit();
}

$user_data = get_user_data($_SESSION['user_id']);
$user_id = $_SESSION['user_id'];
$message = '';
$error = '';

// جلب الإعدادات
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('withdraw_wait_hours', 'group_name', 'min_withdraw')");
$stmt->execute();
$settings_data = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$withdraw_wait_hours = $settings_data['withdraw_wait_hours'] ?? 24;
$group_name = $settings_data['group_name'] ?? 'اسم الجروب';
$min_withdraw = $settings_data['min_withdraw'] ?? 100;

// معالجة طلب السحب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['request_withdraw'])) {
    $robux_amount = (int)$_POST['robux_amount'];
    
    if ($robux_amount < $min_withdraw) {
        $error = "الحد الأدنى للسحب هو {$min_withdraw} روبكس";
    } elseif ($robux_amount > $user_data['robux_balance']) {
        $error = "رصيدك غير كافي";
    } else {
        // التحقق من وجود طلب معلق
        $stmt = $pdo->prepare("SELECT id FROM withdraw_requests WHERE user_id = ? AND status = 'pending'");
        $stmt->execute([$user_id]);
        
        if ($stmt->fetch()) {
            $error = "لديك طلب سحب معلق بالفعل. انتظر حتى يتم معالجته";
        } else {
            // إنشاء طلب السحب
            $pdo->beginTransaction();
            try {
                $stmt = $pdo->prepare("INSERT INTO withdraw_requests (user_id, robux_amount) VALUES (?, ?)");
                $stmt->execute([$user_id, $robux_amount]);
                
                // خصم المبلغ من الرصيد
                $stmt = $pdo->prepare("UPDATE users SET robux_balance = robux_balance - ? WHERE id = ?");
                $stmt->execute([$robux_amount, $user_id]);
                
                $pdo->commit();
                $message = "تم إرسال طلب السحب بنجاح! سيتم مراجعته خلال {$withdraw_wait_hours} ساعة";
                
                // تحديث بيانات المستخدم
                $user_data = get_user_data($user_id);
            } catch (Exception $e) {
                $pdo->rollback();
                $error = "حدث خطأ أثناء إرسال الطلب";
            }
        }
    }
}

// جلب طلبات السحب للمستخدم
$stmt = $pdo->prepare("SELECT * FROM withdraw_requests WHERE user_id = ? ORDER BY created_at DESC");
$stmt->execute([$user_id]);
$withdraw_history = $stmt->fetchAll();

// التحقق من وجود طلب معلق
$stmt = $pdo->prepare("SELECT * FROM withdraw_requests WHERE user_id = ? AND status = 'pending' ORDER BY created_at DESC LIMIT 1");
$stmt->execute([$user_id]);
$pending_request = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سحب روبكس - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="dashboard.php">لوحة التحكم</a>
                    <a href="earn.php">المهام</a>
                    <a href="withdraw.php" class="active">سحب روبكس</a>
                    <?php if (is_admin()): ?>
                        <a href="admin.php">الأدمن</a>
                    <?php endif; ?>
                    <a href="logout.php">تسجيل الخروج</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="withdraw-section">
            <div class="container">
                <div class="withdraw-header">
                    <h1>💰 سحب روبكس</h1>
                    <p>حول نقاطك إلى روبكس حقيقي</p>
                    <div class="user-balance">
                        رصيدك المتاح: <span class="robux-count"><?php echo $user_data['robux_balance']; ?> روبكس</span>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        ✅ <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        ❌ <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if ($pending_request): ?>
                    <div class="pending-request">
                        <h3>⏳ طلب سحب معلق</h3>
                        <div class="request-details">
                            <p><strong>المبلغ:</strong> <?php echo $pending_request['robux_amount']; ?> روبكس</p>
                            <p><strong>تاريخ الطلب:</strong> <?php echo date('Y/m/d H:i', strtotime($pending_request['created_at'])); ?></p>
                            <p><strong>الحالة:</strong> في انتظار المراجعة</p>
                        </div>
                        <div class="wait-message">
                            <p>🕒 يرجى الانتظار حتى <?php echo $withdraw_wait_hours; ?> ساعة لمعالجة طلبك</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="withdraw-form-section">
                        <h3>📝 طلب سحب جديد</h3>
                        
                        <?php if ($user_data['robux_balance'] < $min_withdraw): ?>
                            <div class="insufficient-balance">
                                <p>رصيدك الحالي أقل من الحد الأدنى للسحب</p>
                                <p>الحد الأدنى: <strong><?php echo $min_withdraw; ?> روبكس</strong></p>
                                <a href="earn.php" class="btn btn-primary">أكمل المزيد من المهام</a>
                            </div>
                        <?php else: ?>
                            <form method="POST" class="withdraw-form">
                                <div class="form-group">
                                    <label for="robux_amount">مبلغ الروبكس المطلوب سحبه</label>
                                    <input type="number" id="robux_amount" name="robux_amount" 
                                           min="<?php echo $min_withdraw; ?>" 
                                           max="<?php echo $user_data['robux_balance']; ?>" 
                                           value="<?php echo $min_withdraw; ?>" required>
                                    <small>الحد الأدنى: <?php echo $min_withdraw; ?> روبكس | الحد الأقصى: <?php echo $user_data['robux_balance']; ?> روبكس</small>
                                </div>

                                <div class="withdraw-info">
                                    <h4>📋 معلومات مهمة:</h4>
                                    <ul>
                                        <li>سيتم تحويل الروبكس إلى جروب: <strong><?php echo htmlspecialchars($group_name); ?></strong></li>
                                        <li>وقت المعالجة: حتى <?php echo $withdraw_wait_hours; ?> ساعة</li>
                                        <li>سيتم خصم المبلغ من رصيدك فوراً</li>
                                        <li>تأكد من انضمامك للجروب قبل الطلب</li>
                                    </ul>
                                </div>

                                <button type="submit" name="request_withdraw" class="btn btn-success btn-full"
                                        onclick="return confirm('هل أنت متأكد من طلب سحب هذا المبلغ؟')">
                                    🚀 اطلب السحب
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($withdraw_history)): ?>
                    <div class="withdraw-history">
                        <h3>📊 تاريخ السحوبات</h3>
                        <div class="history-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($withdraw_history as $request): ?>
                                        <tr>
                                            <td><?php echo $request['robux_amount']; ?> روبكس</td>
                                            <td><?php echo date('Y/m/d H:i', strtotime($request['created_at'])); ?></td>
                                            <td>
                                                <span class="status status-<?php echo $request['status']; ?>">
                                                    <?php echo $request['status'] == 'pending' ? 'معلق' : 'مدفوع'; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="withdraw-tips">
                    <h3>💡 نصائح للسحب</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <div class="tip-icon">👥</div>
                            <h4>انضم للجروب</h4>
                            <p>تأكد من انضمامك لجروب <?php echo htmlspecialchars($group_name); ?></p>
                        </div>
                        <div class="tip-card">
                            <div class="tip-icon">⏰</div>
                            <h4>كن صبوراً</h4>
                            <p>المعالجة تستغرق حتى <?php echo $withdraw_wait_hours; ?> ساعة</p>
                        </div>
                        <div class="tip-card">
                            <div class="tip-icon">💎</div>
                            <h4>اجمع أكثر</h4>
                            <p>كلما جمعت أكثر، كلما سحبت أكثر</p>
                        </div>
                        <div class="tip-card">
                            <div class="tip-icon">🔒</div>
                            <h4>آمن 100%</h4>
                            <p>جميع المعاملات آمنة ومضمونة</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
</body>
</html>
