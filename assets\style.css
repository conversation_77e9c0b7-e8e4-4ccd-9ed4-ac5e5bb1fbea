/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #fff;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* شريط التنقل */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.nav-brand h1 {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 700;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 5px rgba(255, 107, 107, 0.5)); }
    to { filter: drop-shadow(0 0 20px rgba(78, 205, 196, 0.8)); }
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #fff;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
    box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
}

.btn-warning {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* القسم الرئيسي */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding-top: 80px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

.hero-content {
    text-align: center;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
}

.gradient-text {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 150px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* الأشكال المتحركة */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* الأقسام */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features {
    padding: 5rem 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.feature-card p {
    opacity: 0.8;
    line-height: 1.6;
}

/* قسم المهام */
.tasks-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
}

.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.task-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-card:hover::before {
    opacity: 1;
}

.task-card:hover {
    transform: translateY(-5px);
    border-color: rgba(78, 205, 196, 0.5);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.task-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #fff;
}

.task-reward {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.task-description {
    opacity: 0.8;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.task-button {
    position: relative;
    z-index: 2;
}

.tasks-info {
    margin-top: 3rem;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-card h3 {
    margin-bottom: 1.5rem;
    color: #4ecdc4;
}

.info-card ul {
    list-style: none;
}

.info-card li {
    padding: 0.5rem 0;
    opacity: 0.9;
}

/* قسم السحب */
.withdraw-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.withdraw-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-bottom: 4rem;
}

.withdraw-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    text-align: center;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.withdraw-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.withdraw-card:hover::before {
    opacity: 1;
}

.withdraw-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.robux-card {
    border-top: 3px solid #ff6b6b;
}

.gamepass-card {
    border-top: 3px solid #4ecdc4;
}

.withdraw-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
}

.withdraw-card h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #fff;
    position: relative;
    z-index: 2;
}

.withdraw-details {
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.withdraw-details p {
    margin-bottom: 0.8rem;
    opacity: 0.9;
}

.withdraw-details strong {
    color: #4ecdc4;
}

.withdraw-requirements {
    margin-top: 4rem;
}

.withdraw-requirements h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #f093fb;
}

.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.requirement-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.requirement-item i {
    font-size: 2rem;
    color: #4ecdc4;
    margin-top: 0.5rem;
}

.requirement-item h4 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.requirement-item p {
    opacity: 0.8;
    line-height: 1.6;
}

/* كيف يعمل */
.how-it-works {
    padding: 5rem 0;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.step-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.step-content p {
    opacity: 0.8;
    line-height: 1.6;
}

/* التذييل */
.footer {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #4ecdc4;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #fff;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #4ecdc4;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

.footer-stats {
    display: flex;
    gap: 2rem;
}

.footer-stat {
    text-align: center;
}

.footer-stat .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.footer-stat .stat-label {
    font-size: 0.9rem;
    opacity: 0.7;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    margin: 5% auto;
    padding: 3rem;
    border-radius: 25px;
    width: 90%;
    max-width: 500px;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff6b6b;
}

.modal-content h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #fff;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.modal-footer {
    text-align: center;
    margin-top: 1.5rem;
    opacity: 0.8;
}

.modal-footer a {
    color: #4ecdc4;
    text-decoration: none;
    font-weight: 600;
}

.modal-footer a:hover {
    color: #ff6b6b;
}

/* لوحة التحكم */
.dashboard {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-balance {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    display: inline-block;
    font-weight: 600;
    font-size: 1.2rem;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dashboard-card h3 {
    margin-bottom: 1rem;
    color: #4ecdc4;
}

/* صفحة الأكواد */
.codes-section {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
}

.code-generator {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 3rem;
}

.code-input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.code-input-group input {
    flex: 1;
}

.generated-code {
    background: rgba(78, 205, 196, 0.1);
    border: 2px solid #4ecdc4;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: #4ecdc4;
    margin-top: 1rem;
    word-break: break-all;
}

.codes-list {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.code-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.code-item:last-child {
    border-bottom: none;
}

.code-details {
    flex: 1;
}

.code-value {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    color: #4ecdc4;
    font-size: 1.1rem;
}

.code-info {
    font-size: 0.9rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.code-actions {
    display: flex;
    gap: 0.5rem;
}

/* صفحة الدعوات */
.invite-section {
    min-height: 100vh;
    padding-top: 100px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.invite-link-generator {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 25px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    margin-bottom: 3rem;
}

.invite-link {
    background: rgba(78, 205, 196, 0.1);
    border: 2px solid #4ecdc4;
    padding: 1rem;
    border-radius: 15px;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    color: #4ecdc4;
    margin: 1rem 0;
    word-break: break-all;
}

.invite-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.invite-stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.invite-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4ecdc4;
    display: block;
}

.invite-stat-label {
    opacity: 0.8;
    margin-top: 0.5rem;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid,
    .tasks-grid,
    .withdraw-options,
    .requirements-grid,
    .steps-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 10% auto;
        padding: 2rem;
    }

    .code-input-group {
        flex-direction: column;
    }

    .code-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .code-actions {
        width: 100%;
        justify-content: center;
    }
}

/* تأثيرات إضافية */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.shake {
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.success-message {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    margin: 1rem 0;
    animation: slideIn 0.3s ease;
}

.error-message {
    background: linear-gradient(45deg, #ff6b6b, #f5576c);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    text-align: center;
    margin: 1rem 0;
    animation: shake 0.5s ease;
}
