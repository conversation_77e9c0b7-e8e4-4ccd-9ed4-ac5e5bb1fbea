<?php
/**
 * Rix Website Configuration File
 * تكوين موقع Rix
 */

// منع الوصول المباشر للملف
if (!defined('RIX_ACCESS')) {
    die('Access denied');
}

// إعدادات الموقع العامة
define('SITE_NAME', 'Rix');
define('SITE_TITLE', 'Rix - اربح روبكس مجاناً');
define('SITE_DESCRIPTION', 'موقع آمن لربح نقاط الروبكس من خلال إكمال المهام البسيطة');
define('SITE_URL', 'https://rix.optikl.ink');
define('SITE_VERSION', '1.0.0');

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'cdwvgbvox_rix');
define('DB_USER', 'CS');
define('DB_PASS', '@!47XKiDzxmpm3ta');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('SESSION_LIFETIME', 3600 * 24); // 24 ساعة
define('PASSWORD_MIN_LENGTH', 6);
define('USERNAME_MIN_LENGTH', 3);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات الروبكس والمهام
define('DEFAULT_MIN_WITHDRAW', 100);
define('DEFAULT_MAX_WITHDRAW', 10000);
define('DEFAULT_WITHDRAW_WAIT_HOURS', 24);
define('MAX_TASKS_PER_DAY', 50);
define('DEFAULT_GROUP_NAME', 'Rix Robux Group');

// إعدادات البريد الإلكتروني (للمستقبل)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Rix Team');

// إعدادات التطوير
define('DEBUG_MODE', false); // تغيير إلى true للتطوير
define('LOG_ERRORS', true);
define('DISPLAY_ERRORS', false);

// إعدادات الملفات
define('UPLOAD_MAX_SIZE', 2 * 1024 * 1024); // 2MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة

// إعدادات API (للمستقبل)
define('API_ENABLED', false);
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // طلب في الساعة

// إعدادات الإشعارات
define('NOTIFICATIONS_ENABLED', true);
define('NOTIFICATION_TYPES', [
    'task_completed',
    'withdrawal_approved',
    'withdrawal_rejected',
    'new_task_available'
]);

// إعدادات الصفحات
define('ITEMS_PER_PAGE', 20);
define('ADMIN_ITEMS_PER_PAGE', 50);

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// إعدادات PHP
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
    ini_set('display_errors', 0);
}

ini_set('log_errors', LOG_ERRORS);
ini_set('max_execution_time', 30);
ini_set('memory_limit', '128M');

// إعدادات المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// إعدادات الحماية من CSRF
define('CSRF_TOKEN_LENGTH', 32);
define('CSRF_TOKEN_LIFETIME', 3600);

// إعدادات التحقق من البوت
define('CAPTCHA_ENABLED', false);
define('RECAPTCHA_SITE_KEY', '');
define('RECAPTCHA_SECRET_KEY', '');

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', true);
define('BACKUP_INTERVAL', 86400); // يومياً
define('BACKUP_RETENTION_DAYS', 30);

// إعدادات الإحصائيات
define('ANALYTICS_ENABLED', true);
define('GOOGLE_ANALYTICS_ID', '');

// إعدادات وسائل التواصل الاجتماعي
define('SOCIAL_LINKS', [
    'facebook' => '',
    'twitter' => '',
    'instagram' => '',
    'youtube' => '',
    'discord' => ''
]);

// إعدادات الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'الموقع تحت الصيانة. سنعود قريباً!');

// إعدادات الأداء
define('ENABLE_GZIP', true);
define('ENABLE_BROWSER_CACHE', true);
define('STATIC_FILES_VERSION', '1.0.0');

// إعدادات الأمان المتقدمة
define('ENABLE_2FA', false); // للمستقبل
define('ENABLE_IP_WHITELIST', false);
define('ALLOWED_IPS', []);

// إعدادات التسجيل
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE_PATH', __DIR__ . '/logs/');
define('MAX_LOG_FILE_SIZE', 10 * 1024 * 1024); // 10MB

// إعدادات المحتوى
define('ENABLE_RICH_TEXT', false);
define('MAX_TASK_DESCRIPTION_LENGTH', 1000);
define('MAX_TASK_TITLE_LENGTH', 255);

// إعدادات الإحالات (للمستقبل)
define('REFERRAL_ENABLED', false);
define('REFERRAL_BONUS', 50);
define('REFERRAL_COMMISSION', 10); // نسبة مئوية

// إعدادات المكافآت اليومية (للمستقبل)
define('DAILY_BONUS_ENABLED', false);
define('DAILY_BONUS_AMOUNT', 10);
define('STREAK_BONUS_MULTIPLIER', 1.5);

// إعدادات المستويات (للمستقبل)
define('LEVELS_ENABLED', false);
define('LEVEL_UP_BONUS', 100);
define('TASKS_PER_LEVEL', 10);

// إعدادات الإشعارات المتقدمة
define('PUSH_NOTIFICATIONS_ENABLED', false);
define('EMAIL_NOTIFICATIONS_ENABLED', false);
define('SMS_NOTIFICATIONS_ENABLED', false);

// إعدادات التكامل مع Roblox
define('ROBLOX_API_ENABLED', false);
define('ROBLOX_API_KEY', '');
define('ROBLOX_GROUP_ID', '');

// إعدادات الدفع البديلة (للمستقبل)
define('PAYPAL_ENABLED', false);
define('PAYPAL_CLIENT_ID', '');
define('PAYPAL_CLIENT_SECRET', '');

// إعدادات الألعاب المصغرة (للمستقبل)
define('MINI_GAMES_ENABLED', false);
define('SPIN_WHEEL_ENABLED', false);
define('DAILY_QUIZ_ENABLED', false);

// إعدادات المسابقات (للمستقبل)
define('CONTESTS_ENABLED', false);
define('WEEKLY_CONTEST_PRIZE', 1000);
define('MONTHLY_CONTEST_PRIZE', 5000);

// إعدادات VIP (للمستقبل)
define('VIP_SYSTEM_ENABLED', false);
define('VIP_BONUS_MULTIPLIER', 2);
define('VIP_MONTHLY_COST', 500);

// إعدادات الشراكات (للمستقبل)
define('PARTNERSHIPS_ENABLED', false);
define('PARTNER_COMMISSION', 5); // نسبة مئوية

// دالة للحصول على إعداد من قاعدة البيانات
function get_setting($key, $default = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        
        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// دالة لحفظ إعداد في قاعدة البيانات
function set_setting($key, $value) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        return $stmt->execute([$key, $value, $value]);
    } catch (Exception $e) {
        return false;
    }
}

// دالة للتحقق من وضع الصيانة
function is_maintenance_mode() {
    return MAINTENANCE_MODE || get_setting('maintenance_mode', false);
}

// دالة للتحقق من تفعيل ميزة
function is_feature_enabled($feature) {
    $features = [
        'referral' => REFERRAL_ENABLED,
        'daily_bonus' => DAILY_BONUS_ENABLED,
        'levels' => LEVELS_ENABLED,
        'mini_games' => MINI_GAMES_ENABLED,
        'contests' => CONTESTS_ENABLED,
        'vip' => VIP_SYSTEM_ENABLED,
        'partnerships' => PARTNERSHIPS_ENABLED
    ];
    
    return isset($features[$feature]) ? $features[$feature] : false;
}

// تحديد ثابت للوصول
define('RIX_CONFIG_LOADED', true);
?>
