// نظام السحب المتطور
class WithdrawSystem {
    constructor() {
        this.withdrawHistory = [];
        this.withdrawStats = {
            totalWithdrawn: 0,
            withdrawCount: 0
        };
        this.init();
    }

    init() {
        this.checkLoginStatus();
        this.loadWithdrawData();
        this.setupEventListeners();
        this.updateUI();
    }

    checkLoginStatus() {
        if (!rix.currentUser) {
            window.location.href = 'index.html';
            return;
        }
    }

    loadWithdrawData() {
        const savedHistory = localStorage.getItem('withdrawHistory_' + rix.currentUser.username);
        if (savedHistory) {
            this.withdrawHistory = JSON.parse(savedHistory);
        }

        const savedStats = localStorage.getItem('withdrawStats_' + rix.currentUser.username);
        if (savedStats) {
            this.withdrawStats = JSON.parse(savedStats);
        }
    }

    saveWithdrawData() {
        localStorage.setItem('withdrawHistory_' + rix.currentUser.username, JSON.stringify(this.withdrawHistory));
        localStorage.setItem('withdrawStats_' + rix.currentUser.username, JSON.stringify(this.withdrawStats));
    }

    setupEventListeners() {
        // نموذج سحب الروبكس
        const robuxForm = document.getElementById('robuxWithdrawForm');
        if (robuxForm) {
            robuxForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.processRobuxWithdraw();
            });
        }

        // نموذج طلب الجيم باس
        const gamepassForm = document.getElementById('gamepassWithdrawForm');
        if (gamepassForm) {
            gamepassForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.processGamepassWithdraw();
            });
        }
    }

    updateUI() {
        // تحديث الرصيد
        document.getElementById('userBalance').textContent = `${rix.userBalance} روبكس`;
        document.getElementById('currentBalance').textContent = `${rix.userBalance} روبكس`;

        // تحديث الإحصائيات
        document.getElementById('totalWithdrawn').textContent = this.withdrawStats.totalWithdrawn.toLocaleString();
        document.getElementById('withdrawCount').textContent = this.withdrawStats.withdrawCount;

        // تحديث سجل السحب
        this.updateWithdrawHistory();
    }

    updateWithdrawHistory() {
        const historyContainer = document.getElementById('withdrawHistory');
        if (!historyContainer) return;

        if (this.withdrawHistory.length === 0) {
            historyContainer.innerHTML = `
                <div class="no-history">
                    <i class="fas fa-inbox"></i>
                    <h3>لا توجد عمليات سحب</h3>
                    <p>ابدأ بطلب أول عملية سحب لك</p>
                </div>
            `;
            return;
        }

        historyContainer.innerHTML = `
            <div class="history-header">
                <div class="header-item">التاريخ</div>
                <div class="header-item">النوع</div>
                <div class="header-item">المبلغ</div>
                <div class="header-item">الحالة</div>
                <div class="header-item">التفاصيل</div>
            </div>
            ${this.withdrawHistory.map(withdraw => `
                <div class="history-row">
                    <div class="history-item">
                        <i class="fas fa-calendar"></i>
                        ${new Date(withdraw.date).toLocaleDateString('ar-SA')}
                    </div>
                    <div class="history-item">
                        <i class="fas fa-${this.getTypeIcon(withdraw.type)}"></i>
                        ${this.getTypeText(withdraw.type)}
                    </div>
                    <div class="history-item">
                        <i class="fas fa-coins"></i>
                        ${withdraw.amount} روبكس
                    </div>
                    <div class="history-item">
                        <span class="status-badge ${withdraw.status}">
                            ${this.getStatusText(withdraw.status)}
                        </span>
                    </div>
                    <div class="history-item">
                        <button class="btn btn-sm btn-info" onclick="withdrawSystem.showWithdrawDetails(${withdraw.id})">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                    </div>
                </div>
            `).join('')}
        `;
    }

    getTypeIcon(type) {
        const icons = {
            'robux': 'coins',
            'gamepass': 'gift',
            'giftcard': 'credit-card'
        };
        return icons[type] || 'question';
    }

    getTypeText(type) {
        const types = {
            'robux': 'روبكس',
            'gamepass': 'جيم باس',
            'giftcard': 'بطاقة هدايا'
        };
        return types[type] || 'غير محدد';
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'معلق',
            'processing': 'قيد المعالجة',
            'completed': 'مكتمل',
            'rejected': 'مرفوض'
        };
        return statuses[status] || 'غير محدد';
    }

    processRobuxWithdraw() {
        const username = document.getElementById('robloxUsername').value;
        const amount = parseInt(document.getElementById('withdrawAmount').value);
        const termsAccepted = document.getElementById('confirmTerms').checked;

        // التحقق من البيانات
        if (!username || !amount || !termsAccepted) {
            rix.showErrorMessage('يرجى ملء جميع الحقول والموافقة على الشروط');
            return;
        }

        if (amount < 100) {
            rix.showErrorMessage('الحد الأدنى للسحب هو 100 روبكس');
            return;
        }

        if (amount > 10000) {
            rix.showErrorMessage('الحد الأقصى للسحب هو 10,000 روبكس');
            return;
        }

        if (amount > rix.userBalance) {
            rix.showErrorMessage('رصيدك غير كافي لهذه العملية');
            return;
        }

        // إنشاء طلب السحب
        const withdrawRequest = {
            id: Date.now(),
            type: 'robux',
            amount: amount,
            username: username,
            date: new Date().toISOString(),
            status: 'pending'
        };

        // خصم المبلغ من الرصيد
        rix.userBalance -= amount;
        rix.saveUserData();

        // إضافة الطلب للسجل
        this.withdrawHistory.unshift(withdrawRequest);
        this.withdrawStats.withdrawCount++;
        this.saveWithdrawData();

        // تحديث الواجهة
        this.updateUI();

        // إغلاق النافذة
        closeModal('robuxWithdrawModal');

        // عرض رسالة نجاح
        rix.showSuccessMessage(`تم إرسال طلب سحب ${amount} روبكس بنجاح! سيتم المعالجة خلال 24 ساعة`);

        // محاكاة معالجة الطلب
        setTimeout(() => {
            this.updateWithdrawStatus(withdrawRequest.id, 'processing');
        }, 5000);

        setTimeout(() => {
            this.updateWithdrawStatus(withdrawRequest.id, 'completed');
        }, 30000);

        // مسح النموذج
        document.getElementById('robuxWithdrawForm').reset();
    }

    processGamepassWithdraw() {
        const username = document.getElementById('gamepassRobloxUsername').value;
        const groupConfirm = document.getElementById('groupMemberConfirm').checked;
        const tasksConfirm = document.getElementById('tasksConfirm').checked;

        // التحقق من البيانات
        if (!username || !groupConfirm || !tasksConfirm) {
            rix.showErrorMessage('يرجى ملء جميع الحقول وتأكيد الشروط');
            return;
        }

        if (rix.userBalance < 500) {
            rix.showErrorMessage('تحتاج إلى 500 روبكس على الأقل لطلب جيم باس');
            return;
        }

        // إنشاء طلب الجيم باس
        const gamepassRequest = {
            id: Date.now(),
            type: 'gamepass',
            amount: 500,
            username: username,
            date: new Date().toISOString(),
            status: 'pending',
            gamepassValue: 800
        };

        // خصم المبلغ من الرصيد
        rix.userBalance -= 500;
        rix.saveUserData();

        // إضافة الطلب للسجل
        this.withdrawHistory.unshift(gamepassRequest);
        this.withdrawStats.withdrawCount++;
        this.saveWithdrawData();

        // تحديث الواجهة
        this.updateUI();

        // إغلاق النافذة
        closeModal('gamepassWithdrawModal');

        // عرض رسالة نجاح
        rix.showSuccessMessage('تم إرسال طلب الجيم باس بنجاح! سيتم إرساله خلال 24 ساعة');

        // محاكاة معالجة الطلب
        setTimeout(() => {
            this.updateWithdrawStatus(gamepassRequest.id, 'processing');
        }, 10000);

        setTimeout(() => {
            this.updateWithdrawStatus(gamepassRequest.id, 'completed');
        }, 60000);

        // مسح النموذج
        document.getElementById('gamepassWithdrawForm').reset();
    }

    updateWithdrawStatus(withdrawId, newStatus) {
        const withdraw = this.withdrawHistory.find(w => w.id === withdrawId);
        if (withdraw) {
            withdraw.status = newStatus;
            
            if (newStatus === 'completed') {
                this.withdrawStats.totalWithdrawn += withdraw.amount;
                
                // إشعار بالإكمال
                rix.showSuccessMessage(`تم إكمال طلب ${this.getTypeText(withdraw.type)} بنجاح! 🎉`);
            }
            
            this.saveWithdrawData();
            this.updateUI();
        }
    }

    showWithdrawDetails(withdrawId) {
        const withdraw = this.withdrawHistory.find(w => w.id === withdrawId);
        if (!withdraw) return;

        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'withdrawDetailsModal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close" onclick="closeModal('withdrawDetailsModal')">&times;</span>
                <h2><i class="fas fa-info-circle"></i> تفاصيل عملية السحب</h2>
                <div class="withdraw-details">
                    <div class="detail-row">
                        <span class="detail-label">رقم العملية:</span>
                        <span class="detail-value">#${withdraw.id}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">النوع:</span>
                        <span class="detail-value">${this.getTypeText(withdraw.type)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">المبلغ:</span>
                        <span class="detail-value">${withdraw.amount} روبكس</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">اسم المستخدم:</span>
                        <span class="detail-value">${withdraw.username}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">التاريخ:</span>
                        <span class="detail-value">${new Date(withdraw.date).toLocaleString('ar-SA')}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            <span class="status-badge ${withdraw.status}">
                                ${this.getStatusText(withdraw.status)}
                            </span>
                        </span>
                    </div>
                    ${withdraw.type === 'gamepass' ? `
                        <div class="detail-row">
                            <span class="detail-label">قيمة الجيم باس:</span>
                            <span class="detail-value">${withdraw.gamepassValue}+ روبكس</span>
                        </div>
                    ` : ''}
                </div>
                <div class="withdraw-timeline">
                    <h4>مراحل المعالجة:</h4>
                    <div class="timeline">
                        <div class="timeline-item ${withdraw.status !== 'rejected' ? 'completed' : ''}">
                            <i class="fas fa-paper-plane"></i>
                            <span>تم إرسال الطلب</span>
                        </div>
                        <div class="timeline-item ${['processing', 'completed'].includes(withdraw.status) ? 'completed' : ''}">
                            <i class="fas fa-cog"></i>
                            <span>قيد المعالجة</span>
                        </div>
                        <div class="timeline-item ${withdraw.status === 'completed' ? 'completed' : ''}">
                            <i class="fas fa-check-circle"></i>
                            <span>تم الإكمال</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'block';
    }
}

// دوال عامة
function showRobuxWithdraw() {
    if (rix.userBalance < 100) {
        rix.showErrorMessage('تحتاج إلى 100 روبكس على الأقل للسحب');
        return;
    }
    document.getElementById('robuxWithdrawModal').style.display = 'block';
}

function showGamepassWithdraw() {
    if (rix.userBalance < 500) {
        rix.showErrorMessage('تحتاج إلى 500 روبكس على الأقل لطلب جيم باس');
        return;
    }
    document.getElementById('gamepassWithdrawModal').style.display = 'block';
}

function showGiftcardWithdraw() {
    if (rix.userBalance < 1000) {
        rix.showErrorMessage('تحتاج إلى 1000 روبكس على الأقل لطلب بطاقة هدايا');
        return;
    }
    rix.showSuccessMessage('ميزة بطاقات الهدايا ستكون متاحة قريباً! 🎁');
}

function logout() {
    rix.logout();
}

// إنشاء نسخة من نظام السحب
const withdrawSystem = new WithdrawSystem();
