// نظام إدارة الأدمن المتطور
class AdminSystem {
    constructor() {
        this.users = [];
        this.adminTasks = [];
        this.adminCodes = [];
        this.withdrawals = [];
        this.activityLog = [];
        this.init();
    }

    init() {
        this.checkAdminAccess();
        this.loadAdminData();
        this.setupEventListeners();
        this.startRealTimeUpdates();
        this.generateSampleData();
    }

    checkAdminAccess() {
        if (!rix.currentUser || !rix.currentUser.isAdmin) {
            window.location.href = 'index.html';
            return;
        }
    }

    loadAdminData() {
        // تحميل بيانات الأدمن من localStorage
        const adminData = localStorage.getItem('adminData');
        if (adminData) {
            const data = JSON.parse(adminData);
            this.users = data.users || [];
            this.adminTasks = data.adminTasks || [];
            this.adminCodes = data.adminCodes || [];
            this.withdrawals = data.withdrawals || [];
            this.activityLog = data.activityLog || [];
        }
        this.updateDashboard();
    }

    saveAdminData() {
        const adminData = {
            users: this.users,
            adminTasks: this.adminTasks,
            adminCodes: this.adminCodes,
            withdrawals: this.withdrawals,
            activityLog: this.activityLog
        };
        localStorage.setItem('adminData', JSON.stringify(adminData));
    }

    setupEventListeners() {
        // مراقبة تغيير نوع المهمة
        const taskTarget = document.getElementById('taskTarget');
        if (taskTarget) {
            taskTarget.addEventListener('change', (e) => {
                const specificUser = document.getElementById('specificUser');
                if (e.target.value === 'specific') {
                    specificUser.style.display = 'block';
                    specificUser.required = true;
                } else {
                    specificUser.style.display = 'none';
                    specificUser.required = false;
                }
            });
        }

        // فلتر طلبات السحب
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                filterButtons.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.filterWithdrawals(e.target.getAttribute('data-status'));
            });
        });
    }

    generateSampleData() {
        // إنشاء بيانات تجريبية للعرض
        if (this.users.length === 0) {
            this.users = [
                {
                    id: 1,
                    username: 'user123',
                    balance: 250,
                    joinDate: new Date(Date.now() - 86400000 * 5).toISOString(),
                    lastActive: new Date().toISOString(),
                    completedTasks: 12,
                    totalEarned: 450,
                    level: 3,
                    isOnline: true
                },
                {
                    id: 2,
                    username: 'gamer456',
                    balance: 180,
                    joinDate: new Date(Date.now() - 86400000 * 10).toISOString(),
                    lastActive: new Date(Date.now() - 3600000).toISOString(),
                    completedTasks: 8,
                    totalEarned: 320,
                    level: 2,
                    isOnline: false
                },
                {
                    id: 3,
                    username: 'robux_hunter',
                    balance: 500,
                    joinDate: new Date(Date.now() - 86400000 * 15).toISOString(),
                    lastActive: new Date(Date.now() - 1800000).toISOString(),
                    completedTasks: 25,
                    totalEarned: 850,
                    level: 5,
                    isOnline: true
                }
            ];

            this.withdrawals = [
                {
                    id: 1,
                    userId: 1,
                    username: 'user123',
                    amount: 100,
                    type: 'robux',
                    status: 'pending',
                    requestDate: new Date().toISOString(),
                    robloxUsername: 'user123_roblox'
                },
                {
                    id: 2,
                    userId: 3,
                    username: 'robux_hunter',
                    amount: 200,
                    type: 'robux',
                    status: 'completed',
                    requestDate: new Date(Date.now() - 86400000).toISOString(),
                    processedDate: new Date(Date.now() - 43200000).toISOString(),
                    robloxUsername: 'hunter_roblox'
                }
            ];

            this.activityLog = [
                {
                    id: 1,
                    type: 'user_register',
                    message: 'مستخدم جديد: user123',
                    timestamp: new Date().toISOString()
                },
                {
                    id: 2,
                    type: 'task_completed',
                    message: 'robux_hunter أكمل مهمة "مشاهدة فيديو"',
                    timestamp: new Date(Date.now() - 1800000).toISOString()
                },
                {
                    id: 3,
                    type: 'withdrawal_request',
                    message: 'طلب سحب جديد من user123 - 100 روبكس',
                    timestamp: new Date(Date.now() - 3600000).toISOString()
                }
            ];

            this.saveAdminData();
        }
    }

    updateDashboard() {
        // تحديث الإحصائيات السريعة
        document.getElementById('totalUsers').textContent = this.users.length;
        document.getElementById('onlineUsers').textContent = this.users.filter(u => u.isOnline).length;
        document.getElementById('totalRobux').textContent = this.users.reduce((sum, u) => sum + u.totalEarned, 0);
        document.getElementById('activeCodes').textContent = this.adminCodes.filter(c => c.isActive).length;

        // تحديث قوائم البيانات
        this.updateUsersList();
        this.updateTasksList();
        this.updateCodesList();
        this.updateWithdrawalsList();
        this.updateActivityLog();
        this.updateStats();
    }

    updateUsersList() {
        const usersList = document.getElementById('usersList');
        if (!usersList) return;

        if (this.users.length === 0) {
            usersList.innerHTML = '<p class="no-data">لا يوجد مستخدمين</p>';
            return;
        }

        usersList.innerHTML = this.users.map(user => `
            <div class="user-item" onclick="showUserDetails(${user.id})">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                    ${user.isOnline ? '<div class="online-indicator"></div>' : ''}
                </div>
                <div class="user-info">
                    <div class="user-name">${user.username}</div>
                    <div class="user-stats">
                        <span><i class="fas fa-coins"></i> ${user.balance} روبكس</span>
                        <span><i class="fas fa-tasks"></i> ${user.completedTasks} مهمة</span>
                        <span><i class="fas fa-level-up-alt"></i> مستوى ${user.level}</span>
                    </div>
                </div>
                <div class="user-actions">
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); addRobuxToUser(${user.id})">
                        <i class="fas fa-plus"></i> إضافة روبكس
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="event.stopPropagation(); editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); banUser(${user.id})">
                        <i class="fas fa-ban"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateTasksList() {
        const tasksList = document.getElementById('adminTasksList');
        if (!tasksList) return;

        if (this.adminTasks.length === 0) {
            tasksList.innerHTML = '<p class="no-data">لا توجد مهام مُنشأة</p>';
            return;
        }

        tasksList.innerHTML = this.adminTasks.map(task => `
            <div class="task-item">
                <div class="task-info">
                    <div class="task-title">${task.title}</div>
                    <div class="task-details">
                        <span class="task-reward">${task.reward} روبكس</span>
                        <span class="task-target">${this.getTaskTargetText(task.target, task.specificUser)}</span>
                        <span class="task-type">${this.getTaskTypeText(task.type)}</span>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-sm btn-success" onclick="toggleTask(${task.id})">
                        ${task.isActive ? '<i class="fas fa-pause"></i> إيقاف' : '<i class="fas fa-play"></i> تفعيل'}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteTask(${task.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateCodesList() {
        const codesList = document.getElementById('adminCodesList');
        if (!codesList) return;

        if (this.adminCodes.length === 0) {
            codesList.innerHTML = '<p class="no-data">لا توجد أكواد مُنشأة</p>';
            return;
        }

        codesList.innerHTML = this.adminCodes.map(code => `
            <div class="code-item">
                <div class="code-info">
                    <div class="code-value">${code.code}</div>
                    <div class="code-details">
                        <span>${code.points} نقطة</span>
                        <span>${code.currentUses}/${code.maxUses} استخدام</span>
                        <span class="code-status ${code.isActive ? 'active' : 'inactive'}">
                            ${code.isActive ? 'نشط' : 'معطل'}
                        </span>
                    </div>
                </div>
                <div class="code-actions">
                    <button class="btn btn-sm btn-info" onclick="copyCode('${code.code}')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="toggleCode(${code.id})">
                        ${code.isActive ? '<i class="fas fa-pause"></i>' : '<i class="fas fa-play"></i>'}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCode(${code.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateWithdrawalsList() {
        const withdrawalsList = document.getElementById('withdrawalsList');
        if (!withdrawalsList) return;

        if (this.withdrawals.length === 0) {
            withdrawalsList.innerHTML = '<p class="no-data">لا توجد طلبات سحب</p>';
            return;
        }

        withdrawalsList.innerHTML = this.withdrawals.map(withdrawal => `
            <div class="withdrawal-item">
                <div class="withdrawal-info">
                    <div class="withdrawal-user">
                        <strong>${withdrawal.username}</strong>
                        <span class="withdrawal-amount">${withdrawal.amount} روبكس</span>
                    </div>
                    <div class="withdrawal-details">
                        <span>Roblox: ${withdrawal.robloxUsername}</span>
                        <span>تاريخ الطلب: ${new Date(withdrawal.requestDate).toLocaleDateString('ar-SA')}</span>
                        <span class="withdrawal-status status-${withdrawal.status}">
                            ${this.getStatusText(withdrawal.status)}
                        </span>
                    </div>
                </div>
                <div class="withdrawal-actions">
                    ${withdrawal.status === 'pending' ? `
                        <button class="btn btn-sm btn-success" onclick="approveWithdrawal(${withdrawal.id})">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="rejectWithdrawal(${withdrawal.id})">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-info" onclick="viewWithdrawalDetails(${withdrawal.id})">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateActivityLog() {
        const activityLog = document.getElementById('activityLog');
        if (!activityLog) return;

        if (this.activityLog.length === 0) {
            activityLog.innerHTML = '<p class="no-data">لا توجد أنشطة</p>';
            return;
        }

        activityLog.innerHTML = this.activityLog.slice(-10).reverse().map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-message">${activity.message}</div>
                    <div class="activity-time">${this.getTimeAgo(activity.timestamp)}</div>
                </div>
            </div>
        `).join('');
    }

    updateStats() {
        // تحديث الإحصائيات المتقدمة
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());

        document.getElementById('newUsers').textContent = this.users.filter(u => 
            new Date(u.joinDate) >= todayStart
        ).length;

        document.getElementById('completedTasks').textContent = this.users.reduce((sum, u) => 
            sum + u.completedTasks, 0
        );

        document.getElementById('withdrawRequests').textContent = this.withdrawals.filter(w => 
            new Date(w.requestDate) >= todayStart
        ).length;

        document.getElementById('usedCodes').textContent = this.adminCodes.reduce((sum, c) => 
            sum + c.currentUses, 0
        );
    }

    // دوال مساعدة
    getTaskTargetText(target, specificUser) {
        switch(target) {
            case 'all': return 'جميع المستخدمين';
            case 'specific': return `مستخدم محدد: ${specificUser}`;
            case 'level': return 'مستوى محدد';
            default: return 'غير محدد';
        }
    }

    getTaskTypeText(type) {
        const types = {
            'social': 'وسائل التواصل',
            'video': 'مشاهدة فيديو',
            'app': 'تطبيقات',
            'group': 'جروبات',
            'survey': 'استبيان'
        };
        return types[type] || type;
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'معلق',
            'processing': 'قيد المعالجة',
            'completed': 'مكتمل',
            'rejected': 'مرفوض'
        };
        return statuses[status] || status;
    }

    getActivityIcon(type) {
        const icons = {
            'user_register': 'user-plus',
            'task_completed': 'check-circle',
            'withdrawal_request': 'money-bill-wave',
            'code_used': 'gift',
            'admin_action': 'cog'
        };
        return icons[type] || 'info-circle';
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (days > 0) return `منذ ${days} يوم`;
        if (hours > 0) return `منذ ${hours} ساعة`;
        if (minutes > 0) return `منذ ${minutes} دقيقة`;
        return 'الآن';
    }

    startRealTimeUpdates() {
        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            this.updateDashboard();
        }, 30000);
    }

    // إضافة نشاط جديد للسجل
    addActivity(type, message) {
        this.activityLog.push({
            id: Date.now(),
            type: type,
            message: message,
            timestamp: new Date().toISOString()
        });
        this.saveAdminData();
        this.updateActivityLog();
    }
}

// إنشاء نسخة من نظام الأدمن
const adminSystem = new AdminSystem();

// دوال الأدمن العامة
function createTask() {
    const title = document.getElementById('taskTitle').value;
    const description = document.getElementById('taskDescription').value;
    const reward = parseInt(document.getElementById('taskReward').value);
    const type = document.getElementById('taskType').value;
    const target = document.getElementById('taskTarget').value;
    const link = document.getElementById('taskLink').value;
    const duration = parseInt(document.getElementById('taskDuration').value) || 60;
    const difficulty = document.getElementById('taskDifficulty').value;
    const specificUser = document.getElementById('specificUser').value;
    const requireLogin = document.getElementById('taskRequireLogin').checked;
    const trackTime = document.getElementById('taskTrackTime').checked;
    const limitedTime = document.getElementById('taskLimitedTime').checked;

    if (!title || !description || !reward || !link) {
        rix.showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (target === 'specific' && !specificUser) {
        rix.showErrorMessage('يرجى تحديد اسم المستخدم');
        return;
    }

    // التحقق من صحة الرابط
    try {
        new URL(link);
    } catch (e) {
        rix.showErrorMessage('يرجى إدخال رابط صحيح');
        return;
    }

    if (duration < 60) {
        rix.showErrorMessage('مدة البقاء يجب أن تكون 60 ثانية على الأقل');
        return;
    }

    const newTask = {
        id: Date.now(),
        title: title,
        description: description,
        reward: reward,
        type: type,
        target: target,
        specificUser: specificUser,
        link: link,
        duration: duration,
        difficulty: difficulty,
        requireLogin: requireLogin,
        trackTime: trackTime,
        limitedTime: limitedTime,
        isActive: true,
        createdAt: new Date().toISOString(),
        createdBy: rix.currentUser.username,
        completedBy: [],
        totalCompletions: 0
    };

    adminSystem.adminTasks.push(newTask);
    adminSystem.saveAdminData();
    adminSystem.updateTasksList();

    // مسح النموذج
    document.getElementById('taskTitle').value = '';
    document.getElementById('taskDescription').value = '';
    document.getElementById('taskReward').value = '';
    document.getElementById('taskLink').value = '';
    document.getElementById('taskDuration').value = '60';
    document.getElementById('specificUser').value = '';
    document.getElementById('taskRequireLogin').checked = true;
    document.getElementById('taskTrackTime').checked = true;
    document.getElementById('taskLimitedTime').checked = false;

    adminSystem.addActivity('admin_action', `تم إنشاء مهمة جديدة: ${title} - رابط: ${link}`);
    rix.showSuccessMessage('تم إنشاء المهمة بنجاح!');
}

function generateAdminCode() {
    const points = parseInt(document.getElementById('codePoints').value);
    const uses = parseInt(document.getElementById('codeUses').value);
    const prefix = document.getElementById('codePrefix').value || 'RIX';
    const expiry = document.getElementById('codeExpiry').value;

    if (!points || !uses) {
        rix.showErrorMessage('يرجى تحديد النقاط وعدد الاستخدامات');
        return;
    }

    const code = generateRandomCode(prefix);
    const expiryDate = expiry !== 'never' ?
        new Date(Date.now() + parseInt(expiry) * 24 * 60 * 60 * 1000).toISOString() : null;

    const newCode = {
        id: Date.now(),
        code: code,
        points: points,
        maxUses: uses,
        currentUses: 0,
        isActive: true,
        createdAt: new Date().toISOString(),
        expiryDate: expiryDate,
        createdBy: rix.currentUser.username
    };

    adminSystem.adminCodes.push(newCode);
    adminSystem.saveAdminData();
    adminSystem.updateCodesList();
    adminSystem.updateDashboard();

    // عرض الكود المُنشأ
    const generatedCodeDiv = document.getElementById('generatedCode');
    generatedCodeDiv.innerHTML = `
        <div class="success-code">
            <h4>تم إنشاء الكود بنجاح!</h4>
            <div class="code-display">${code}</div>
            <p>النقاط: ${points} | الاستخدامات: ${uses}</p>
            <button class="btn btn-primary btn-sm" onclick="copyCode('${code}')">
                <i class="fas fa-copy"></i> نسخ الكود
            </button>
        </div>
    `;
    generatedCodeDiv.style.display = 'block';

    // مسح النموذج
    document.getElementById('codePoints').value = '';
    document.getElementById('codeUses').value = '';
    document.getElementById('codePrefix').value = '';

    adminSystem.addActivity('admin_action', `تم إنشاء كود جديد: ${code}`);
    rix.showSuccessMessage('تم إنشاء الكود بنجاح!');
}

function generateRandomCode(prefix = 'RIX') {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

function copyCode(code) {
    navigator.clipboard.writeText(code).then(() => {
        rix.showSuccessMessage('تم نسخ الكود بنجاح!');
    });
}

function toggleTask(taskId) {
    const task = adminSystem.adminTasks.find(t => t.id === taskId);
    if (task) {
        task.isActive = !task.isActive;
        adminSystem.saveAdminData();
        adminSystem.updateTasksList();
        adminSystem.addActivity('admin_action',
            `تم ${task.isActive ? 'تفعيل' : 'إيقاف'} المهمة: ${task.title}`);
        rix.showSuccessMessage(`تم ${task.isActive ? 'تفعيل' : 'إيقاف'} المهمة بنجاح`);
    }
}

function deleteTask(taskId) {
    if (confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
        const taskIndex = adminSystem.adminTasks.findIndex(t => t.id === taskId);
        if (taskIndex > -1) {
            const task = adminSystem.adminTasks[taskIndex];
            adminSystem.adminTasks.splice(taskIndex, 1);
            adminSystem.saveAdminData();
            adminSystem.updateTasksList();
            adminSystem.addActivity('admin_action', `تم حذف المهمة: ${task.title}`);
            rix.showSuccessMessage('تم حذف المهمة بنجاح');
        }
    }
}

function toggleCode(codeId) {
    const code = adminSystem.adminCodes.find(c => c.id === codeId);
    if (code) {
        code.isActive = !code.isActive;
        adminSystem.saveAdminData();
        adminSystem.updateCodesList();
        adminSystem.updateDashboard();
        adminSystem.addActivity('admin_action',
            `تم ${code.isActive ? 'تفعيل' : 'إيقاف'} الكود: ${code.code}`);
        rix.showSuccessMessage(`تم ${code.isActive ? 'تفعيل' : 'إيقاف'} الكود بنجاح`);
    }
}

function deleteCode(codeId) {
    if (confirm('هل أنت متأكد من حذف هذا الكود؟')) {
        const codeIndex = adminSystem.adminCodes.findIndex(c => c.id === codeId);
        if (codeIndex > -1) {
            const code = adminSystem.adminCodes[codeIndex];
            adminSystem.adminCodes.splice(codeIndex, 1);
            adminSystem.saveAdminData();
            adminSystem.updateCodesList();
            adminSystem.updateDashboard();
            adminSystem.addActivity('admin_action', `تم حذف الكود: ${code.code}`);
            rix.showSuccessMessage('تم حذف الكود بنجاح');
        }
    }
}

function addRobuxToUser(userId) {
    const amount = prompt('كم روبكس تريد إضافته؟');
    if (amount && !isNaN(amount) && parseInt(amount) > 0) {
        const user = adminSystem.users.find(u => u.id === userId);
        if (user) {
            user.balance += parseInt(amount);
            user.totalEarned += parseInt(amount);
            adminSystem.saveAdminData();
            adminSystem.updateUsersList();
            adminSystem.addActivity('admin_action',
                `تم إضافة ${amount} روبكس للمستخدم: ${user.username}`);
            rix.showSuccessMessage(`تم إضافة ${amount} روبكس بنجاح`);
        }
    }
}

function banUser(userId) {
    if (confirm('هل أنت متأكد من حظر هذا المستخدم؟')) {
        const user = adminSystem.users.find(u => u.id === userId);
        if (user) {
            user.isBanned = true;
            adminSystem.saveAdminData();
            adminSystem.updateUsersList();
            adminSystem.addActivity('admin_action', `تم حظر المستخدم: ${user.username}`);
            rix.showSuccessMessage('تم حظر المستخدم بنجاح');
        }
    }
}

function approveWithdrawal(withdrawalId) {
    const withdrawal = adminSystem.withdrawals.find(w => w.id === withdrawalId);
    if (withdrawal) {
        withdrawal.status = 'completed';
        withdrawal.processedDate = new Date().toISOString();
        adminSystem.saveAdminData();
        adminSystem.updateWithdrawalsList();
        adminSystem.addActivity('admin_action',
            `تم الموافقة على طلب سحب: ${withdrawal.username} - ${withdrawal.amount} روبكس`);
        rix.showSuccessMessage('تم الموافقة على طلب السحب');
    }
}

function rejectWithdrawal(withdrawalId) {
    const reason = prompt('سبب الرفض (اختياري):');
    const withdrawal = adminSystem.withdrawals.find(w => w.id === withdrawalId);
    if (withdrawal) {
        withdrawal.status = 'rejected';
        withdrawal.rejectionReason = reason;
        withdrawal.processedDate = new Date().toISOString();

        // إرجاع الروبكس للمستخدم
        const user = adminSystem.users.find(u => u.id === withdrawal.userId);
        if (user) {
            user.balance += withdrawal.amount;
        }

        adminSystem.saveAdminData();
        adminSystem.updateWithdrawalsList();
        adminSystem.updateUsersList();
        adminSystem.addActivity('admin_action',
            `تم رفض طلب سحب: ${withdrawal.username} - ${withdrawal.amount} روبكس`);
        rix.showSuccessMessage('تم رفض طلب السحب وإرجاع الروبكس');
    }
}

function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const userItems = document.querySelectorAll('.user-item');

    userItems.forEach(item => {
        const username = item.querySelector('.user-name').textContent.toLowerCase();
        if (username.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

function refreshUsers() {
    adminSystem.updateUsersList();
    rix.showSuccessMessage('تم تحديث قائمة المستخدمين');
}

function refreshWithdrawals() {
    adminSystem.updateWithdrawalsList();
    rix.showSuccessMessage('تم تحديث طلبات السحب');
}

function clearLogs() {
    if (confirm('هل أنت متأكد من مسح سجل الأنشطة؟')) {
        adminSystem.activityLog = [];
        adminSystem.saveAdminData();
        adminSystem.updateActivityLog();
        rix.showSuccessMessage('تم مسح سجل الأنشطة');
    }
}

function updateStats() {
    adminSystem.updateStats();
}

function logout() {
    rix.logout();
}
