// نظام الدعوات المتطور
class InviteSystem {
    constructor() {
        this.inviteData = {
            totalInvites: 0,
            activeInvites: 0,
            inviteEarnings: 0,
            commissionEarnings: 0,
            invitedUsers: [],
            dailyInvites: {},
            currentLevel: 'none'
        };
        this.inviteLink = '';
        this.levels = {
            bronze: { required: 5, bonus: 50, commission: 10 },
            silver: { required: 15, bonus: 150, commission: 12 },
            gold: { required: 50, bonus: 500, commission: 15 },
            diamond: { required: 100, bonus: 1000, commission: 20 }
        };
        this.init();
    }

    init() {
        this.checkLoginStatus();
        this.loadInviteData();
        this.generateInviteLink();
        this.updateUI();
        this.startTimers();
    }

    checkLoginStatus() {
        if (!rix.currentUser) {
            window.location.href = 'index.html';
            return;
        }
    }

    loadInviteData() {
        const saved = localStorage.getItem('inviteData_' + rix.currentUser.username);
        if (saved) {
            this.inviteData = { ...this.inviteData, ...JSON.parse(saved) };
        }
    }

    saveInviteData() {
        localStorage.setItem('inviteData_' + rix.currentUser.username, JSON.stringify(this.inviteData));
    }

    generateInviteLink() {
        const baseUrl = window.location.origin;
        this.inviteLink = `${baseUrl}?ref=${rix.currentUser.username}`;
        document.getElementById('inviteLink').value = this.inviteLink;
    }

    updateUI() {
        // تحديث الرصيد
        document.getElementById('userBalance').textContent = `${rix.userBalance} روبكس`;

        // تحديث إحصائيات الدعوات
        document.getElementById('totalInvites').textContent = this.inviteData.totalInvites;
        document.getElementById('activeInvites').textContent = this.inviteData.activeInvites;
        document.getElementById('inviteEarnings').textContent = this.inviteData.inviteEarnings;
        document.getElementById('commissionEarnings').textContent = this.inviteData.commissionEarnings;

        // تحديث مستويات الدعوات
        this.updateLevelsProgress();

        // تحديث قائمة المدعوين
        this.updateInvitedUsersList();

        // تحديث الإحصائيات المتقدمة
        this.updateAdvancedStats();
    }

    updateLevelsProgress() {
        const totalInvites = this.inviteData.totalInvites;

        // البرونزي
        const bronzeProgress = Math.min((totalInvites / this.levels.bronze.required) * 100, 100);
        document.getElementById('bronzeProgress').style.width = bronzeProgress + '%';
        document.getElementById('bronzeText').textContent = `${Math.min(totalInvites, this.levels.bronze.required)}/${this.levels.bronze.required}`;

        // الفضي
        const silverProgress = Math.min((totalInvites / this.levels.silver.required) * 100, 100);
        document.getElementById('silverProgress').style.width = silverProgress + '%';
        document.getElementById('silverText').textContent = `${Math.min(totalInvites, this.levels.silver.required)}/${this.levels.silver.required}`;

        // الذهبي
        const goldProgress = Math.min((totalInvites / this.levels.gold.required) * 100, 100);
        document.getElementById('goldProgress').style.width = goldProgress + '%';
        document.getElementById('goldText').textContent = `${Math.min(totalInvites, this.levels.gold.required)}/${this.levels.gold.required}`;

        // الماسي
        const diamondProgress = Math.min((totalInvites / this.levels.diamond.required) * 100, 100);
        document.getElementById('diamondProgress').style.width = diamondProgress + '%';
        document.getElementById('diamondText').textContent = `${Math.min(totalInvites, this.levels.diamond.required)}/${this.levels.diamond.required}`;

        // تحديث المستوى الحالي
        this.updateCurrentLevel();
    }

    updateCurrentLevel() {
        const totalInvites = this.inviteData.totalInvites;
        let newLevel = 'none';

        if (totalInvites >= this.levels.diamond.required) {
            newLevel = 'diamond';
        } else if (totalInvites >= this.levels.gold.required) {
            newLevel = 'gold';
        } else if (totalInvites >= this.levels.silver.required) {
            newLevel = 'silver';
        } else if (totalInvites >= this.levels.bronze.required) {
            newLevel = 'bronze';
        }

        // إذا وصل لمستوى جديد
        if (newLevel !== this.inviteData.currentLevel && newLevel !== 'none') {
            this.levelUp(newLevel);
        }

        this.inviteData.currentLevel = newLevel;
    }

    levelUp(level) {
        const levelData = this.levels[level];
        rix.userBalance += levelData.bonus;
        this.inviteData.inviteEarnings += levelData.bonus;

        rix.saveUserData();
        this.saveInviteData();

        rix.showSuccessMessage(`🎉 تهانينا! وصلت للمستوى ${this.getLevelName(level)}! حصلت على ${levelData.bonus} روبكس مكافأة`);
    }

    getLevelName(level) {
        const names = {
            'bronze': 'البرونزي',
            'silver': 'الفضي',
            'gold': 'الذهبي',
            'diamond': 'الماسي'
        };
        return names[level] || 'غير محدد';
    }

    updateInvitedUsersList() {
        const usersList = document.getElementById('invitedUsersList');
        if (!usersList) return;

        if (this.inviteData.invitedUsers.length === 0) {
            usersList.innerHTML = `
                <div class="no-invites">
                    <i class="fas fa-user-plus"></i>
                    <h3>لم تدع أي مستخدمين بعد</h3>
                    <p>ابدأ بمشاركة رابط الدعوة الخاص بك</p>
                </div>
            `;
            return;
        }

        usersList.innerHTML = `
            <div class="table-header">
                <div class="header-item">المستخدم</div>
                <div class="header-item">تاريخ الانضمام</div>
                <div class="header-item">الحالة</div>
                <div class="header-item">الأرباح</div>
                <div class="header-item">العمولة</div>
            </div>
            ${this.inviteData.invitedUsers.map(user => `
                <div class="table-row">
                    <div class="table-item">
                        <div class="user-info">
                            <i class="fas fa-user"></i>
                            <span>${user.username}</span>
                        </div>
                    </div>
                    <div class="table-item">
                        ${new Date(user.joinDate).toLocaleDateString('ar-SA')}
                    </div>
                    <div class="table-item">
                        <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                            ${user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </div>
                    <div class="table-item">
                        ${user.totalEarnings} روبكس
                    </div>
                    <div class="table-item">
                        ${user.commissionEarned} روبكس
                    </div>
                </div>
            `).join('')}
        `;
    }

    updateAdvancedStats() {
        const today = new Date().toDateString();
        const todayInvites = this.inviteData.dailyInvites[today] || 0;
        document.getElementById('todayInvites').textContent = todayInvites;

        // حساب دعوات الأسبوع
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - 7);
        let weekInvites = 0;
        
        Object.keys(this.inviteData.dailyInvites).forEach(date => {
            if (new Date(date) >= weekStart) {
                weekInvites += this.inviteData.dailyInvites[date];
            }
        });
        
        document.getElementById('weekInvites').textContent = weekInvites;

        // معدل التحويل (نسبة المستخدمين النشطين)
        const conversionRate = this.inviteData.totalInvites > 0 ? 
            Math.round((this.inviteData.activeInvites / this.inviteData.totalInvites) * 100) : 0;
        document.getElementById('conversionRate').textContent = conversionRate + '%';

        // أفضل يوم
        let bestDay = '-';
        let maxInvites = 0;
        Object.keys(this.inviteData.dailyInvites).forEach(date => {
            if (this.inviteData.dailyInvites[date] > maxInvites) {
                maxInvites = this.inviteData.dailyInvites[date];
                bestDay = new Date(date).toLocaleDateString('ar-SA');
            }
        });
        document.getElementById('bestDay').textContent = bestDay;

        // تحديث مسابقة الشهر
        document.getElementById('yourContestInvites').textContent = `${this.inviteData.totalInvites} دعوة`;
    }

    // محاكاة دعوة مستخدم جديد (للاختبار)
    simulateNewInvite() {
        const newUser = {
            username: 'User' + Math.floor(Math.random() * 1000),
            joinDate: new Date().toISOString(),
            isActive: Math.random() > 0.3, // 70% نشطين
            totalEarnings: Math.floor(Math.random() * 500),
            commissionEarned: 0
        };

        newUser.commissionEarned = Math.floor(newUser.totalEarnings * 0.1);

        this.inviteData.invitedUsers.push(newUser);
        this.inviteData.totalInvites++;
        
        if (newUser.isActive) {
            this.inviteData.activeInvites++;
        }

        // مكافأة الدعوة
        rix.userBalance += 25;
        this.inviteData.inviteEarnings += 25;
        this.inviteData.commissionEarnings += newUser.commissionEarned;

        // تحديث الإحصائيات اليومية
        const today = new Date().toDateString();
        this.inviteData.dailyInvites[today] = (this.inviteData.dailyInvites[today] || 0) + 1;

        rix.saveUserData();
        this.saveInviteData();
        this.updateUI();

        rix.showSuccessMessage(`🎉 مبروك! انضم مستخدم جديد عبر دعوتك وحصلت على 25 روبكس!`);
    }

    startTimers() {
        // تحديث مؤقت المسابقة
        setInterval(() => {
            this.updateContestTimer();
        }, 1000);

        // محاكاة دعوات جديدة كل فترة (للاختبار)
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% احتمال كل 30 ثانية
                this.simulateNewInvite();
            }
        }, 30000);
    }

    updateContestTimer() {
        // حساب الوقت المتبقي لنهاية الشهر
        const now = new Date();
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        const diff = endOfMonth - now;
        
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        
        const timerElement = document.getElementById('contestTimer');
        if (timerElement) {
            timerElement.textContent = `${days} يوم و ${hours} ساعة متبقية`;
        }
    }
}

// دوال عامة
function copyInviteLink() {
    const inviteLink = document.getElementById('inviteLink');
    inviteLink.select();
    document.execCommand('copy');
    rix.showSuccessMessage('تم نسخ رابط الدعوة بنجاح!');
}

function shareToWhatsApp() {
    const message = `🎮 انضم لموقع Rix واربح روبكس مجاناً!\n\n✨ مميزات رائعة:\n• روبكس حقيقي 100%\n• جيم باس مجاني\n• مهام سهلة ومربحة\n• سحب سريع\n\n🔗 سجل الآن: ${inviteSystem.inviteLink}`;
    const url = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
}

function shareToTelegram() {
    const message = `🎮 اربح روبكس مجاناً مع Rix!\n\n${inviteSystem.inviteLink}`;
    const url = `https://t.me/share/url?url=${encodeURIComponent(inviteSystem.inviteLink)}&text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
}

function shareToFacebook() {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(inviteSystem.inviteLink)}`;
    window.open(url, '_blank');
}

function shareToTwitter() {
    const message = `🎮 اربح روبكس مجاناً مع Rix! انضم الآن:`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(inviteSystem.inviteLink)}`;
    window.open(url, '_blank');
}

function logout() {
    rix.logout();
}

// إنشاء نسخة من نظام الدعوات
const inviteSystem = new InviteSystem();
