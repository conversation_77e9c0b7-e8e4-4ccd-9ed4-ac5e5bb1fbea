<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الأدمن - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar admin-navbar">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-crown"></i> Rix Admin <span class="admin-badge">ADMIN</span></h1>
            </div>
            <div class="nav-links">
                <a href="dashboard.html" class="nav-link">لوحة المستخدم</a>
                <a href="admin.html" class="nav-link active">لوحة الأدمن</a>
                <div class="admin-info">
                    <i class="fas fa-user-shield"></i>
                    <span id="adminName">Admin</span>
                </div>
                <button class="btn btn-danger" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <!-- لوحة تحكم الأدمن -->
    <div class="admin-dashboard">
        <div class="container">
            <!-- رأس لوحة التحكم -->
            <div class="admin-header">
                <h1><i class="fas fa-tachometer-alt"></i> لوحة تحكم الأدمن</h1>
                <p>إدارة شاملة لموقع Rix</p>
                <div class="admin-quick-stats">
                    <div class="quick-stat">
                        <i class="fas fa-users"></i>
                        <span class="stat-number" id="totalUsers">0</span>
                        <span class="stat-label">إجمالي المستخدمين</span>
                    </div>
                    <div class="quick-stat">
                        <i class="fas fa-user-clock"></i>
                        <span class="stat-number" id="onlineUsers">0</span>
                        <span class="stat-label">متصل الآن</span>
                    </div>
                    <div class="quick-stat">
                        <i class="fas fa-coins"></i>
                        <span class="stat-number" id="totalRobux">0</span>
                        <span class="stat-label">روبكس مدفوع</span>
                    </div>
                </div>
            </div>

            <!-- شبكة لوحة التحكم -->
            <div class="admin-grid">
                <!-- إدارة المستخدمين -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-users-cog"></i> إدارة المستخدمين</h3>
                        <button class="btn btn-primary btn-sm" onclick="refreshUsers()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="search-bar">
                            <input type="text" id="userSearch" placeholder="البحث عن مستخدم..." onkeyup="searchUsers()">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="users-list" id="usersList">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- إدارة المهام -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-tasks"></i> إدارة المهام</h3>
                        <button class="btn btn-success btn-sm" onclick="showCreateTaskModal()">
                            <i class="fas fa-plus"></i> مهمة جديدة
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="task-creation">
                            <div class="form-row">
                                <select id="taskTarget" class="form-control">
                                    <option value="all">جميع المستخدمين</option>
                                    <option value="specific">مستخدم محدد</option>
                                    <option value="level">مستوى محدد</option>
                                </select>
                                <input type="text" id="taskTitle" placeholder="عنوان المهمة" class="form-control">
                            </div>
                            <div class="form-row">
                                <input type="number" id="taskReward" placeholder="المكافأة" class="form-control" min="1">
                                <select id="taskType" class="form-control">
                                    <option value="custom">مهمة مخصصة</option>
                                    <option value="social">وسائل التواصل</option>
                                    <option value="video">مشاهدة فيديو</option>
                                    <option value="app">تطبيقات</option>
                                    <option value="group">جروبات</option>
                                    <option value="survey">استبيان</option>
                                    <option value="website">زيارة موقع</option>
                                </select>
                            </div>
                            <textarea id="taskDescription" placeholder="وصف المهمة" class="form-control" rows="3"></textarea>
                            <div class="form-row">
                                <input type="url" id="taskLink" placeholder="رابط المهمة (مطلوب)" class="form-control" required>
                                <input type="number" id="taskDuration" placeholder="مدة البقاء (بالثواني)" class="form-control" min="60" value="60">
                            </div>
                            <div class="form-row">
                                <input type="text" id="specificUser" placeholder="اسم المستخدم (للمهام المحددة)" class="form-control" style="display: none;">
                                <select id="taskDifficulty" class="form-control">
                                    <option value="easy">سهل</option>
                                    <option value="medium">متوسط</option>
                                    <option value="hard">صعب</option>
                                </select>
                            </div>
                            <div class="task-settings">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="taskRequireLogin" checked>
                                    <span>يتطلب تسجيل دخول</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="taskTrackTime" checked>
                                    <span>تتبع وقت البقاء في الرابط</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="taskLimitedTime">
                                    <span>مهمة محدودة الوقت</span>
                                </label>
                            </div>
                            <button class="btn btn-primary btn-full" onclick="createTask()">
                                <i class="fas fa-plus"></i> إنشاء المهمة
                            </button>
                        </div>
                        <div class="tasks-list" id="adminTasksList">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- مولد الأكواد -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-code"></i> مولد الأكواد</h3>
                        <div class="card-tools">
                            <span class="tool-label">أكواد نشطة: <span id="activeCodes">0</span></span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="code-generator">
                            <div class="form-row">
                                <input type="number" id="codePoints" placeholder="عدد النقاط" class="form-control" min="1" max="10000">
                                <input type="number" id="codeUses" placeholder="عدد الاستخدامات" class="form-control" min="1" max="1000">
                            </div>
                            <div class="form-row">
                                <input type="text" id="codePrefix" placeholder="بادئة الكود (اختياري)" class="form-control" maxlength="5">
                                <select id="codeExpiry" class="form-control">
                                    <option value="never">لا ينتهي</option>
                                    <option value="1">يوم واحد</option>
                                    <option value="7">أسبوع</option>
                                    <option value="30">شهر</option>
                                </select>
                            </div>
                            <button class="btn btn-warning btn-full" onclick="generateAdminCode()">
                                <i class="fas fa-magic"></i> إنشاء كود مكافأة
                            </button>
                            <div id="generatedCode" class="generated-code-display" style="display: none;">
                                <!-- سيتم عرض الكود هنا -->
                            </div>
                        </div>
                        <div class="codes-management">
                            <h4>الأكواد المُنشأة</h4>
                            <div class="codes-list" id="adminCodesList">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات متقدمة -->
                <div class="admin-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-line"></i> إحصائيات متقدمة</h3>
                        <div class="card-tools">
                            <select id="statsFilter" onchange="updateStats()">
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="all">جميع الأوقات</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="stat-content">
                                    <h4>مستخدمين جدد</h4>
                                    <div class="stat-number" id="newUsers">0</div>
                                    <div class="stat-change positive">+12% من الأمس</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div class="stat-content">
                                    <h4>مهام مكتملة</h4>
                                    <div class="stat-number" id="completedTasks">0</div>
                                    <div class="stat-change positive">+8% من الأمس</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stat-content">
                                    <h4>طلبات سحب</h4>
                                    <div class="stat-number" id="withdrawRequests">0</div>
                                    <div class="stat-change negative">-3% من الأمس</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <div class="stat-content">
                                    <h4>أكواد مستخدمة</h4>
                                    <div class="stat-number" id="usedCodes">0</div>
                                    <div class="stat-change positive">+15% من الأمس</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إدارة طلبات السحب -->
                <div class="admin-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-money-check-alt"></i> إدارة طلبات السحب</h3>
                        <div class="card-tools">
                            <button class="btn btn-info btn-sm" onclick="refreshWithdrawals()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="withdrawals-filter">
                            <button class="filter-btn active" data-status="all">جميع الطلبات</button>
                            <button class="filter-btn" data-status="pending">معلقة</button>
                            <button class="filter-btn" data-status="processing">قيد المعالجة</button>
                            <button class="filter-btn" data-status="completed">مكتملة</button>
                            <button class="filter-btn" data-status="rejected">مرفوضة</button>
                        </div>
                        <div class="withdrawals-list" id="withdrawalsList">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- سجل الأنشطة -->
                <div class="admin-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> سجل الأنشطة</h3>
                        <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                            <i class="fas fa-trash"></i> مسح السجل
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="activity-log" id="activityLog">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل المستخدم -->
    <div id="userDetailsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('userDetailsModal')">&times;</span>
            <h2><i class="fas fa-user"></i> تفاصيل المستخدم</h2>
            <div id="userDetailsContent">
                <!-- سيتم ملؤها بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <script src="assets/script.js"></script>
    <script src="assets/admin.js"></script>
</body>
</html>
