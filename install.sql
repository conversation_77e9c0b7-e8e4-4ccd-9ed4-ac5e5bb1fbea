


CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `join_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_admin` tinyint(1) DEFAULT 0,
  `robux_balance` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_username` (`username`),
  KEY `idx_is_admin` (`is_admin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE IF NOT EXISTS `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `reward` int(11) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  <PERSON>IMAR<PERSON> KEY (`id`),
  <PERSON><PERSON>Y `idx_is_active` (`is_active`)
) <PERSON>NGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل المهام
CREATE TABLE IF NOT EXISTS `task_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `task_id` int(11) NOT NULL,
  `completed_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_task` (`user_id`,`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_completed_at` (`completed_at`),
  CONSTRAINT `task_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `task_logs_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE IF NOT EXISTS `withdraw_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `robux_amount` int(11) NOT NULL,
  `status` varchar(20) DEFAULT 'pending',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `processed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `withdraw_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `settings` (`setting_key`, `setting_value`) VALUES
('withdraw_wait_hours', '24'),
('group_name', 'Rix Robux Group'),
('min_withdraw', '100'),
('site_title', 'Rix - اربح روبكس مجاناً'),
('max_withdraw', '10000'),
('daily_task_limit', '10');


INSERT INTO `users` (`username`, `password`, `is_admin`, `robux_balance`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 0);


INSERT INTO `tasks` (`title`, `description`, `reward`, `is_active`) VALUES
('🎥 مشاهدة فيديو ترحيبي', 'شاهد الفيديو الترحيبي للموقع لمدة دقيقة واحدة واحصل على نقاط مجانية', 15, 1),
('👍 إعجاب بصفحة الفيسبوك', 'سجل إعجابك بصفحتنا الرسمية على الفيسبوك وادعم المجتمع', 25, 1),
('📱 تحميل التطبيق المجاني', 'حمل تطبيقنا المجاني من متجر التطبيقات واستمتع بالمزيد من المميزات', 50, 1),
('🔔 تفعيل الإشعارات', 'فعل الإشعارات لتكون أول من يعلم بالمهام الجديدة والعروض الخاصة', 20, 1),
('📝 إكمال الملف الشخصي', 'أكمل معلومات ملفك الشخصي لتحسين تجربتك على الموقع', 30, 1),
('🎮 انضمام لجروب Roblox', 'انضم إلى جروبنا الرسمي في Roblox للحصول على المزيد من الفرص', 40, 1),
('⭐ تقييم الموقع', 'قيم تجربتك على موقعنا وساعدنا في التحسين', 35, 1),
('📢 مشاركة مع الأصدقاء', 'شارك رابط الموقع مع أصدقائك وساعدهم في ربح الروبكس أيضاً', 45, 1),
('📺 مشاهدة إعلان قصير', 'شاهد إعلان قصير لمدة 30 ثانية لدعم الموقع', 10, 1),
('🎯 إكمال الاستبيان', 'أكمل استبيان قصير عن اهتماماتك في الألعاب (5 دقائق)', 60, 1);


INSERT INTO `users` (`username`, `password`, `is_admin`, `robux_balance`) VALUES
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 150),
('user2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 75),
('user3', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 200);


INSERT INTO `task_logs` (`user_id`, `task_id`) VALUES
(2, 1), (2, 2), (2, 3),
(3, 1), (3, 4),
(4, 1), (4, 2), (4, 3), (4, 4), (4, 5);


INSERT INTO `withdraw_requests` (`user_id`, `robux_amount`, `status`) VALUES
(2, 100, 'pending'),
(4, 150, 'paid');


ALTER TABLE `users` AUTO_INCREMENT = 5;
ALTER TABLE `tasks` AUTO_INCREMENT = 11;
ALTER TABLE `task_logs` AUTO_INCREMENT = 9;
ALTER TABLE `withdraw_requests` AUTO_INCREMENT = 3;
ALTER TABLE `settings` AUTO_INCREMENT = 7;


SELECT 'تم تثبيت قاعدة بيانات Rix بنجاح!' as 'Installation Status',
       'admin' as 'Admin Username', 
       'admin123' as 'Admin Password',
       'يمكنك الآن استخدام الموقع' as 'Next Step';
