<?php
session_start();
require_once 'includes/db.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (is_logged_in()) {
    header('Location: dashboard.php');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = clean_input($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        $stmt = $pdo->prepare("SELECT id, username, password FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            header('Location: dashboard.php');
            exit();
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Rix</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php"><h1>🎮 Rix</h1></a>
                </div>
                <div class="nav-links">
                    <a href="index.php">الرئيسية</a>
                    <a href="register.php">إنشاء حساب</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="auth-section">
            <div class="container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h2>🔐 تسجيل الدخول</h2>
                        <p>مرحباً بعودتك! سجل دخولك لمتابعة ربح الروبكس</p>
                    </div>

                    <?php if ($error): ?>
                        <div class="alert alert-error">
                            ❌ <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="auth-form">
                        <div class="form-group">
                            <label for="username">اسم المستخدم</label>
                            <input type="text" id="username" name="username" required 
                                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                                   placeholder="أدخل اسم المستخدم">
                        </div>

                        <div class="form-group">
                            <label for="password">كلمة المرور</label>
                            <input type="password" id="password" name="password" required 
                                   placeholder="أدخل كلمة المرور">
                        </div>

                        <button type="submit" class="btn btn-primary btn-full">
                            تسجيل الدخول 🚀
                        </button>
                    </form>

                    <div class="auth-footer">
                        <p>ليس لديك حساب؟ <a href="register.php">أنشئ حساباً جديداً</a></p>
                    </div>

                    <div class="login-benefits">
                        <h3>🎯 استمر في ربح الروبكس</h3>
                        <div class="benefits-grid">
                            <div class="benefit">
                                <span class="benefit-icon">💎</span>
                                <span>مهام جديدة يومياً</span>
                            </div>
                            <div class="benefit">
                                <span class="benefit-icon">⚡</span>
                                <span>سحب سريع</span>
                            </div>
                            <div class="benefit">
                                <span class="benefit-icon">🔒</span>
                                <span>حساب آمن</span>
                            </div>
                            <div class="benefit">
                                <span class="benefit-icon">🎮</span>
                                <span>روبكس حقيقي</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Rix - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="assets/script.js"></script>
</body>
</html>
